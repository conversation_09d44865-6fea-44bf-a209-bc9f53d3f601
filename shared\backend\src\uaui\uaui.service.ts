import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { ProvidersService } from "../providers/providers.service";
import { ToolsService } from "../tools/tools.service";
import { AgentsService } from "../agents/agents.service";
import { SessionsService } from "../sessions/sessions.service";
import { ApixService } from "../apix/apix.service";
import { BillingService } from "../billing/billing.service";
import { AnalyticsService } from "../analytics/analytics.service";
import { SmartProviderSelector } from "./smart-provider-selector";
import { StateManager } from "./state-manager";
import { EventBus } from "./event-bus";
import { RouterEngine } from "./router-engine";

export interface UAUIRequest {
  userId: string;
  organizationId: string;
  sessionId?: string;
  message: string;
  agentId?: string;
  toolId?: string;
  hybridId?: string;
  appType?: string;
  metadata?: Record<string, any>;
  context?: Record<string, any>;
}

export interface UAUIResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
  hitl_request?: {
    type: string;
    data: Record<string, any>;
  };
}

@Injectable()
export class UAUIService {
  private readonly logger = new Logger(UAUIService.name);

  constructor(
    private prisma: PrismaService,
    private providersService: ProvidersService,
    private toolsService: ToolsService,
    private agentsService: AgentsService,
    private sessionsService: SessionsService,
    private apixService: ApixService,
    private billingService: BillingService,
    private analyticsService: AnalyticsService,
    private smartProviderSelector: SmartProviderSelector,
    private stateManager: StateManager,
    private eventBus: EventBus,
    private routerEngine: RouterEngine
  ) {}

  async processRequest(request: UAUIRequest): Promise<UAUIResponse> {
    this.logger.log(`Processing UAUI request from user ${request.userId}`);
    
    try {
      // Track request
      await this.analyticsService.track("uaui_request", {
        userId: request.userId,
        organizationId: request.organizationId,
        sessionId: request.sessionId,
        agentId: request.agentId,
        toolId: request.toolId,
        hybridId: request.hybridId,
        appType: request.appType,
      });

      // Check quota
      await this.billingService.checkQuota(
        request.organizationId,
        "uaui_requests",
        1
      );

      // Get or create session
      const session = await this.getOrCreateSession(request);

      // Emit thinking status
      await this.apixService.emit({
        type: "thinking_status",
        payload: {
          status: "thinking",
          sessionId: session.id,
        },
        organizationId: request.organizationId,
        userId: request.userId,
        sessionId: session.id,
      });

      // Process based on request type
      let response: UAUIResponse;

      if (request.agentId) {
        response = await this.processAgentRequest(request, session);
      } else if (request.toolId) {
        response = await this.processToolRequest(request, session);
      } else if (request.hybridId) {
        response = await this.processHybridRequest(request, session);
      } else {
        // Default to smart routing
        response = await this.processSmartRoutingRequest(request, session);
      }

      // Update session state
      await this.stateManager.updateSessionState(session.id, {
        lastResponse: response.final || "",
        lastInteraction: new Date().toISOString(),
      });

      // Track completion
      await this.analyticsService.track("uaui_response", {
        userId: request.userId,
        organizationId: request.organizationId,
        sessionId: session.id,
        success: !response.error,
        hasToolCall: !!response.tool_call,
        hasHitlRequest: !!response.hitl_request,
      });

      return response;
    } catch (error) {
      this.logger.error(`Error processing UAUI request: ${error.message}`, error.stack);
      
      // Emit error event
      if (request.sessionId) {
        await this.apixService.emit({
          type: "error",
          payload: {
            error: error.message,
            sessionId: request.sessionId,
          },
          organizationId: request.organizationId,
          userId: request.userId,
          sessionId: request.sessionId,
        });
      }

      return {
        error: error.message,
      };
    }
  }

  private async getOrCreateSession(request: UAUIRequest) {
    if (request.sessionId) {
      const existingSession = await this.sessionsService.findOne(request.sessionId);
      if (existingSession) {
        return existingSession;
      }
    }

    // Create new session
    return await this.sessionsService.create({
      title: `Session ${new Date().toISOString()}`,
      context: request.context || {},
      agentId: request.agentId,
      hybridId: request.hybridId,
      userId: request.userId,
      organizationId: request.organizationId,
    });
  }

  private async processAgentRequest(request: UAUIRequest, session: any): Promise<UAUIResponse> {
    // Execute agent with the message
    const result = await this.agentsService.execute(
      request.agentId,
      {
        input: request.message,
        sessionId: session.id,
        context: request.context || {},
      },
      { id: request.userId, organizationId: request.organizationId }
    );

    // Store message in session
    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: request.message,
      role: "USER",
      metadata: request.metadata || {},
    });

    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: result.response.content,
      role: "ASSISTANT",
      metadata: {
        executionId: result.executionId,
        tokens: result.tokens,
        cost: result.cost,
        duration: result.duration,
      },
    });

    return {
      final: result.response.content,
      state_update: {
        executionId: result.executionId,
        tokens: result.tokens,
        cost: result.cost,
        duration: result.duration,
      },
    };
  }

  private async processToolRequest(request: UAUIRequest, session: any): Promise<UAUIResponse> {
    // Parse input from message or use context
    let input: Record<string, any>;
    
    try {
      input = JSON.parse(request.message);
    } catch (e) {
      // If not valid JSON, use as a single input parameter
      input = { input: request.message };
    }

    // Execute tool
    const result = await this.toolsService.execute(
      request.toolId,
      {
        input,
        sessionId: session.id,
        context: request.context || {},
      },
      { id: request.userId, organizationId: request.organizationId }
    );

    // Store message in session
    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: JSON.stringify(input),
      role: "USER",
      metadata: request.metadata || {},
    });

    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: JSON.stringify(result.result),
      role: "TOOL",
      metadata: {
        executionId: result.executionId,
        cost: result.cost,
        duration: result.duration,
      },
    });

    return {
      final: JSON.stringify(result.result),
      state_update: {
        executionId: result.executionId,
        cost: result.cost,
        duration: result.duration,
      },
    };
  }

  private async processHybridRequest(request: UAUIRequest, session: any): Promise<UAUIResponse> {
    // This will be implemented in the HybridsService
    // For now, return a placeholder
    return {
      final: "Hybrid processing not yet implemented",
      state_update: {
        status: "not_implemented",
      },
    };
  }

  private async processSmartRoutingRequest(request: UAUIRequest, session: any): Promise<UAUIResponse> {
    // Analyze the request and determine the best way to handle it
    // For now, default to using a default agent
    
    // Get default agent for the organization
    const defaultAgent = await this.prisma.agent.findFirst({
      where: {
        organizationId: request.organizationId,
        status: "ACTIVE",
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!defaultAgent) {
      return {
        final: "No active agents found for this organization. Please create an agent first.",
        error: "No active agents found",
      };
    }

    // Process with the default agent
    return await this.processAgentRequest(
      {
        ...request,
        agentId: defaultAgent.id,
      },
      session
    );
  }
}