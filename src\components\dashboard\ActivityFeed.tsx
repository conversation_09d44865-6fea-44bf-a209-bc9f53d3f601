"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  CheckCircle,
  AlertCircle,
  Clock,
  Bot,
  Wrench,
  GitBranch,
  Bell,
  Filter,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface ActivityItem {
  id: string;
  type: "agent" | "workflow" | "tool" | "notification";
  title: string;
  description: string;
  status: "success" | "error" | "pending";
  timestamp: string;
  user?: {
    name: string;
    avatar: string;
  };
}

const ActivityFeed = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [filters, setFilters] = useState({
    success: true,
    error: true,
    pending: true,
    agent: true,
    workflow: true,
    tool: true,
    notification: true,
  });

  // Mock data for activity feed
  const activities: ActivityItem[] = [
    {
      id: "1",
      type: "agent",
      title: "Customer Support Agent",
      description:
        "Successfully answered customer inquiry about product features",
      status: "success",
      timestamp: "2 minutes ago",
      user: {
        name: "John Doe",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=john",
      },
    },
    {
      id: "2",
      type: "workflow",
      title: "Lead Qualification Workflow",
      description: "New lead processed and assigned to sales team",
      status: "success",
      timestamp: "15 minutes ago",
      user: {
        name: "Sarah Miller",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=sarah",
      },
    },
    {
      id: "3",
      type: "tool",
      title: "Email Integration",
      description: "Failed to send notification email due to API error",
      status: "error",
      timestamp: "32 minutes ago",
    },
    {
      id: "4",
      type: "notification",
      title: "System Update",
      description: "Platform maintenance scheduled for tonight at 2 AM UTC",
      status: "pending",
      timestamp: "1 hour ago",
    },
    {
      id: "5",
      type: "agent",
      title: "Sales Assistant Agent",
      description: "Generated product recommendation for enterprise client",
      status: "success",
      timestamp: "2 hours ago",
      user: {
        name: "Michael Chen",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=michael",
      },
    },
    {
      id: "6",
      type: "workflow",
      title: "Document Processing",
      description: "Waiting for approval to proceed with document extraction",
      status: "pending",
      timestamp: "3 hours ago",
      user: {
        name: "Lisa Johnson",
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=lisa",
      },
    },
    {
      id: "7",
      type: "tool",
      title: "Database Query Tool",
      description: "Successfully retrieved customer records for analysis",
      status: "success",
      timestamp: "4 hours ago",
    },
    {
      id: "8",
      type: "notification",
      title: "Quota Alert",
      description:
        "Your organization is approaching the monthly API call limit",
      status: "error",
      timestamp: "5 hours ago",
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "agent":
        return <Bot className="h-4 w-4 text-blue-500" />;
      case "tool":
        return <Wrench className="h-4 w-4 text-purple-500" />;
      case "workflow":
        return <GitBranch className="h-4 w-4 text-indigo-500" />;
      case "notification":
        return <Bell className="h-4 w-4 text-orange-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 border-green-200";
      case "error":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "";
    }
  };

  const filteredActivities = activities.filter((activity) => {
    if (activeTab !== "all" && activity.type !== activeTab) return false;
    if (!filters[activity.status]) return false;
    if (!filters[activity.type]) return false;
    return true;
  });

  const toggleFilter = (key: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <Card className="h-full bg-white">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">Activity Feed</CardTitle>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                <Filter className="h-4 w-4" />
                <span className="sr-only">Filter activities</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56" align="end">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium leading-none">Status</h4>
                  <div className="grid gap-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="filter-success"
                        checked={filters.success}
                        onCheckedChange={() => toggleFilter("success")}
                      />
                      <Label htmlFor="filter-success">Success</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="filter-error"
                        checked={filters.error}
                        onCheckedChange={() => toggleFilter("error")}
                      />
                      <Label htmlFor="filter-error">Error</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="filter-pending"
                        checked={filters.pending}
                        onCheckedChange={() => toggleFilter("pending")}
                      />
                      <Label htmlFor="filter-pending">Pending</Label>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium leading-none">Type</h4>
                  <div className="grid gap-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="filter-agent"
                        checked={filters.agent}
                        onCheckedChange={() => toggleFilter("agent")}
                      />
                      <Label htmlFor="filter-agent">Agent</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="filter-tool"
                        checked={filters.tool}
                        onCheckedChange={() => toggleFilter("tool")}
                      />
                      <Label htmlFor="filter-tool">Tool</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="filter-workflow"
                        checked={filters.workflow}
                        onCheckedChange={() => toggleFilter("workflow")}
                      />
                      <Label htmlFor="filter-workflow">Workflow</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="filter-notification"
                        checked={filters.notification}
                        onCheckedChange={() => toggleFilter("notification")}
                      />
                      <Label htmlFor="filter-notification">Notification</Label>
                    </div>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="agent">Agents</TabsTrigger>
            <TabsTrigger value="workflow">Workflows</TabsTrigger>
            <TabsTrigger value="tool">Tools</TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="mt-2">
            <ActivityList
              activities={filteredActivities}
              getStatusIcon={getStatusIcon}
              getTypeIcon={getTypeIcon}
              getStatusColor={getStatusColor}
            />
          </TabsContent>
          <TabsContent value="agent" className="mt-2">
            <ActivityList
              activities={filteredActivities}
              getStatusIcon={getStatusIcon}
              getTypeIcon={getTypeIcon}
              getStatusColor={getStatusColor}
            />
          </TabsContent>
          <TabsContent value="workflow" className="mt-2">
            <ActivityList
              activities={filteredActivities}
              getStatusIcon={getStatusIcon}
              getTypeIcon={getTypeIcon}
              getStatusColor={getStatusColor}
            />
          </TabsContent>
          <TabsContent value="tool" className="mt-2">
            <ActivityList
              activities={filteredActivities}
              getStatusIcon={getStatusIcon}
              getTypeIcon={getTypeIcon}
              getStatusColor={getStatusColor}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

interface ActivityListProps {
  activities: ActivityItem[];
  getStatusIcon: (status: string) => React.ReactNode;
  getTypeIcon: (type: string) => React.ReactNode;
  getStatusColor: (status: string) => string;
}

const ActivityList = ({
  activities,
  getStatusIcon,
  getTypeIcon,
  getStatusColor,
}: ActivityListProps) => {
  if (activities.length === 0) {
    return (
      <div className="flex h-[400px] items-center justify-center">
        <p className="text-sm text-gray-500">
          No activities match your filters
        </p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-[400px] pr-4">
      <div className="space-y-4">
        {activities.map((activity) => (
          <div
            key={activity.id}
            className="flex items-start space-x-4 rounded-lg border p-3 transition-colors hover:bg-gray-50"
          >
            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100">
              {getTypeIcon(activity.type)}
            </div>
            <div className="flex-1 space-y-1">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">{activity.title}</p>
                <Badge
                  variant="outline"
                  className={`${getStatusColor(activity.status)}`}
                >
                  <span className="flex items-center gap-1">
                    {getStatusIcon(activity.status)}
                    <span className="capitalize">{activity.status}</span>
                  </span>
                </Badge>
              </div>
              <p className="text-sm text-gray-500">{activity.description}</p>
              <div className="flex items-center justify-between pt-1">
                <p className="text-xs text-gray-400">{activity.timestamp}</p>
                {activity.user && (
                  <div className="flex items-center space-x-1">
                    <Avatar className="h-5 w-5">
                      <AvatarImage
                        src={activity.user.avatar}
                        alt={activity.user.name}
                      />
                      <AvatarFallback>
                        {activity.user.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs text-gray-500">
                      {activity.user.name}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
};

export default ActivityFeed;
