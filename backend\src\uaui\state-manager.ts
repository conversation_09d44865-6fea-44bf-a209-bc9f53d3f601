import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { ApixService } from "../apix/apix.service";
import { InjectRedis } from "@nestjs/redis";
import Redis from "ioredis";

@Injectable()
export class StateManager {
  private readonly logger = new Logger(StateManager.name);
  private readonly statePrefix = "synapseai:state:";
  private readonly sessionPrefix = "synapseai:session:";
  private readonly stateTTL = 60 * 60 * 24; // 24 hours

  constructor(
    private prisma: PrismaService,
    private apixService: ApixService,
    @InjectRedis() private readonly redis: Redis
  ) {}

  async getSessionState(sessionId: string): Promise<Record<string, any>> {
    try {
      // Try to get from Redis first
      const redisKey = `${this.sessionPrefix}${sessionId}`;
      const cachedState = await this.redis.get(redisKey);

      if (cachedState) {
        return JSON.parse(cachedState);
      }

      // If not in Redis, get from database
      const session = await this.prisma.session.findUnique({
        where: { id: sessionId },
        select: { memory: true },
      });

      if (!session) {
        return {};
      }

      // Cache in Redis for future access
      await this.redis.set(
        redisKey,
        JSON.stringify(session.memory),
        "EX",
        this.stateTTL
      );

      return session.memory as Record<string, any>;
    } catch (error) {
      this.logger.error(
        `Error getting session state: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined
      );
      return {};
    }
  }

  async updateSessionState(
    sessionId: string,
    updates: Record<string, any>
  ): Promise<void> {
    try {
      // Get current state
      const currentState = await this.getSessionState(sessionId);

      // Merge updates
      const newState = {
        ...currentState,
        ...updates,
        _lastUpdated: new Date().toISOString(),
      };

      // Update Redis
      const redisKey = `${this.sessionPrefix}${sessionId}`;
      await this.redis.set(
        redisKey,
        JSON.stringify(newState),
        "EX",
        this.stateTTL
      );

      // Update database
      await this.prisma.session.update({
        where: { id: sessionId },
        data: {
          memory: newState,
          updatedAt: new Date(),
        },
      });

      // Get session details for broadcasting
      const session = await this.prisma.session.findUnique({
        where: { id: sessionId },
        select: { userId: true, organizationId: true },
      });

      if (session) {
        // Broadcast state update
        await this.apixService.emit({
          type: "state_update",
          payload: {
            sessionId,
            state: updates,
            timestamp: new Date().toISOString(),
          },
          organizationId: session.organizationId,
          userId: session.userId,
          sessionId,
        });
      }
    } catch (error) {
      this.logger.error(
        `Error updating session state: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined
      );
      throw error;
    }
  }

  async getAppState(
    appId: string,
    organizationId: string
  ): Promise<Record<string, any>> {
    try {
      const redisKey = `${this.statePrefix}${organizationId}:${appId}`;
      const cachedState = await this.redis.get(redisKey);

      if (cachedState) {
        return JSON.parse(cachedState);
      }

      return {};
    } catch (error) {
      this.logger.error(
        `Error getting app state: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined
      );
      return {};
    }
  }

  async updateAppState(
    appId: string,
    organizationId: string,
    updates: Record<string, any>
  ): Promise<void> {
    try {
      // Get current state
      const currentState = await this.getAppState(appId, organizationId);

      // Merge updates
      const newState = {
        ...currentState,
        ...updates,
        _lastUpdated: new Date().toISOString(),
      };

      // Update Redis
      const redisKey = `${this.statePrefix}${organizationId}:${appId}`;
      await this.redis.set(
        redisKey,
        JSON.stringify(newState),
        "EX",
        this.stateTTL
      );

      // Broadcast state update
      await this.apixService.emit({
        type: "app_state_update",
        payload: {
          appId,
          state: updates,
          timestamp: new Date().toISOString(),
        },
        organizationId,
      });
    } catch (error) {
      this.logger.error(
        `Error updating app state: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined
      );
      throw error;
    }
  }

  async syncState(
    fromAppId: string,
    toAppId: string,
    organizationId: string,
    keys?: string[]
  ): Promise<void> {
    try {
      // Get source state
      const sourceState = await this.getAppState(fromAppId, organizationId);

      // If keys specified, only sync those keys
      let statesToSync: Record<string, any> = {};
      if (keys && keys.length > 0) {
        keys.forEach((key) => {
          if (sourceState[key] !== undefined) {
            statesToSync[key] = sourceState[key];
          }
        });
      } else {
        statesToSync = sourceState;
      }

      // Update target state
      await this.updateAppState(toAppId, organizationId, statesToSync);
    } catch (error) {
      this.logger.error(`Error syncing state: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async clearSessionState(sessionId: string): Promise<void> {
    try {
      // Clear from Redis
      const redisKey = `${this.sessionPrefix}${sessionId}`;
      await this.redis.del(redisKey);

      // Clear from database
      await this.prisma.session.update({
        where: { id: sessionId },
        data: {
          memory: {},
        },
      });
    } catch (error) {
      this.logger.error(
          `Error clearing session state: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error instanceof Error ? error.stack : undefined
      );
      throw error;
    }
  }
}