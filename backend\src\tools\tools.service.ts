import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
  Logger,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { CreateToolDto } from "./dto/create-tool.dto";
import { UpdateToolDto } from "./dto/update-tool.dto";
import { QueryToolsDto } from "./dto/query-tools.dto";
import { ExecuteToolDto } from "./dto/execute-tool.dto";
import { ToolStatus, UserRole } from "@prisma/client";
import { BillingService } from "../billing/billing.service";
import { AnalyticsService } from "../analytics/analytics.service";
import { NotificationsService } from "../notifications/notifications.service";
import { ApixService } from "../apix/apix.service";

@Injectable()
export class ToolsService {
  private readonly logger = new Logger(ToolsService.name);
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second

  constructor(
    private prisma: PrismaService,
    private billingService: BillingService,
    private analyticsService: AnalyticsService,
    private notificationsService: NotificationsService,
    private apixService: ApixService,
  ) {}

  async create(
    createToolDto: CreateToolDto,
    userId: string,
    organizationId: string,
  ): Promise<Tool> {
    // Check quota
    await this.billingService.checkQuota(organizationId, "tools", 1);

    const tool = await this.prisma.tool.create({
      data: {
        ...createToolDto,
        userId,
        organizationId,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Track usage
    await this.billingService.trackUsage(organizationId, "tools", 1, 0, {
      toolId: tool.id,
      action: "create",
    });

    // Track analytics
    await this.analyticsService.track("tool_created", {
      toolId: tool.id,
      organizationId,
      userId,
      category: tool.category,
      status: tool.status,
    });

    return tool as Tool;
  }

  async findAll(
    query: QueryToolsDto,
    userId: string,
    organizationId: string,
    userRole: UserRole,
  ) {
    const {
      page,
      limit,
      search,
      status,
      isPublic,
      tags,
      category,
      sortBy,
      sortOrder,
    } = query;
    const skip = (page - 1) * limit;

    const where: any = {
      OR: [{ organizationId }, { isPublic: true }],
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (isPublic !== undefined) {
      where.isPublic = isPublic;
    }

    if (tags && tags.length > 0) {
      where.tags = {
        hasSome: tags,
      };
    }

    if (category) {
      where.category = category;
    }

    const [tools, total] = await Promise.all([
      this.prisma.tool.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          _count: {
            select: {
              executions: true,
            },
          },
        },
      }),
      this.prisma.tool.count({ where }),
    ]);

    return {
      data: tools,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(
    id: string,
    userId: string,
    organizationId: string,
    userRole: UserRole,
  ): Promise<Tool> {
    const tool = await this.prisma.tool.findFirst({
      where: {
        id,
        OR: [{ organizationId }, { isPublic: true }],
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        executions: {
          take: 10,
          orderBy: {
            createdAt: "desc",
          },
        },
        _count: {
          select: {
            executions: true,
            hybrids: true,
            workflows: true,
          },
        },
      },
    });

    if (!tool) {
      throw new NotFoundException("Tool not found");
    }

    return tool as Tool;
  }

  async update(
    id: string,
    updateToolDto: UpdateToolDto,
    userId: string,
    organizationId: string,
    userRole: UserRole,
  ): Promise<Tool> {
    const existingTool = await this.prisma.tool.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!existingTool) {
      throw new NotFoundException("Tool not found");
    }

    if (
      existingTool.userId !== userId &&
      userRole !== UserRole.ORG_ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException("You can only update your own tools");
    }

    const tool = await this.prisma.tool.update({
      where: { id },
      data: {
        ...updateToolDto,
        version: existingTool.version + 1,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Track analytics
    await this.analyticsService.track("tool_updated", {
      toolId: tool.id,
      organizationId,
      userId,
      version: tool.version,
    });

    return tool as Tool;
  }

  async remove(
    id: string,
    userId: string,
    organizationId: string,
    userRole: UserRole,
  ): Promise<void> {
    const tool = await this.prisma.tool.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!tool) {
      throw new NotFoundException("Tool not found");
    }

    if (
      tool.userId !== userId &&
      userRole !== UserRole.ORG_ADMIN &&
      userRole !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException("You can only delete your own tools");
    }

    await this.prisma.tool.delete({
      where: { id },
    });

    // Track analytics
    await this.analyticsService.track("tool_deleted", {
      toolId: id,
      organizationId,
      userId,
    });
  }

  async execute(
    id: string,
    executeToolDto: ExecuteToolDto,
    userId: string,
    organizationId: string,
  ): Promise<any> {
    const tool = await this.prisma.tool.findFirst({
      where: {
        id,
        OR: [{ organizationId }, { isPublic: true }],
        status: ToolStatus.ACTIVE,
      },
    });

    if (!tool) {
      throw new NotFoundException("Tool not found or not active");
    }

    // Check quota
    await this.billingService.checkQuota(organizationId, "tool_executions", 1);

    const startTime = Date.now();
    let execution;

    try {
      // Create execution record
      execution = await this.prisma.toolExecution.create({
        data: {
          toolId: id,
          status: "RUNNING",
          input: executeToolDto.input,
        },
      });

      // Emit real-time event
      await this.apixService.emitToolEvent(
        "tool_execution_started",
        id,
        organizationId,
        userId,
        { executionId: execution.id, input: executeToolDto.input },
      );

      // Execute the tool with retry logic
      const executionResult = await this.executeToolWithRetry(
        tool,
        executeToolDto.input,
        execution.id,
      );

      const duration = Date.now() - startTime;
      const cost = this.calculateExecutionCost(
        tool,
        duration,
        executionResult.dataProcessed || 0,
      );

      // Update execution record
      await this.prisma.toolExecution.update({
        where: { id: execution.id },
        data: {
          status: "COMPLETED",
          output: executionResult,
          duration,
          cost,
          completedAt: new Date(),
        },
      });

      // Track usage and billing
      await this.billingService.trackUsage(
        organizationId,
        "tool_executions",
        1,
        cost,
        {
          toolId: id,
          executionId: execution.id,
          duration,
          category: tool.category,
        },
      );

      // Track analytics
      await this.analyticsService.track("tool_executed", {
        toolId: id,
        executionId: execution.id,
        organizationId,
        userId,
        status: "COMPLETED",
        duration,
        cost,
        category: tool.category,
      });

      // Emit completion event
      await this.apixService.emitToolEvent(
        "tool_execution_completed",
        id,
        organizationId,
        userId,
        {
          executionId: execution.id,
          duration,
          cost,
          success: true,
        },
      );

      return {
        executionId: execution.id,
        status: "COMPLETED",
        result: executionResult,
        duration,
        cost,
      };
    } catch (error) {
      this.logger.error(`Tool execution failed: ${error.message}`, error.stack);

      if (execution) {
        // Update execution with error
        await this.prisma.toolExecution.update({
          where: { id: execution.id },
          data: {
            status: "FAILED",
            error: error.message,
            completedAt: new Date(),
            duration: Date.now() - startTime,
          },
        });

        // Emit failure event
        await this.apixService.emitToolEvent(
          "tool_execution_failed",
          id,
          organizationId,
          userId,
          {
            executionId: execution.id,
            error: error.message,
            duration: Date.now() - startTime,
          },
        );
      }

      throw error;
    }
  }

  private async executeToolWithRetry(
    tool: any,
    input: Record<string, any>,
    executionId: string,
    attempt: number = 1,
  ): Promise<any> {
    try {
      return await this.executeToolLogic(tool, input, executionId);
    } catch (error) {
      if (attempt < this.maxRetries && this.isRetryableError(error)) {
        this.logger.warn(
          `Tool execution attempt ${attempt} failed, retrying...`,
          error.message,
        );

        // Exponential backoff
        await this.delay(this.retryDelay * Math.pow(2, attempt - 1));

        return this.executeToolWithRetry(tool, input, executionId, attempt + 1);
      }

      throw error;
    }
  }

  private isRetryableError(error: any): boolean {
    // Retry on network errors, timeouts, and 5xx HTTP errors
    const retryablePatterns = [
      /network/i,
      /timeout/i,
      /ECONNRESET/i,
      /ENOTFOUND/i,
      /5\d{2}/, // 5xx HTTP status codes
    ];

    return retryablePatterns.some(
      (pattern) => pattern.test(error.message) || pattern.test(error.code),
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async getExecutions(
    id: string,
    page: number = 1,
    limit: number = 10,
    userId: string,
    organizationId: string,
  ) {
    const tool = await this.prisma.tool.findFirst({
      where: {
        id,
        OR: [{ organizationId }, { isPublic: true }],
      },
    });

    if (!tool) {
      throw new NotFoundException("Tool not found");
    }

    const skip = (page - 1) * limit;

    const [executions, total] = await Promise.all([
      this.prisma.toolExecution.findMany({
        where: { toolId: id },
        skip,
        take: limit,
        orderBy: {
          createdAt: "desc",
        },
      }),
      this.prisma.toolExecution.count({
        where: { toolId: id },
      }),
    ]);

    return {
      data: executions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async duplicate(
    id: string,
    userId: string,
    organizationId: string,
  ): Promise<Tool> {
    const originalTool = await this.prisma.tool.findFirst({
      where: {
        id,
        OR: [{ organizationId }, { isPublic: true }],
      },
    });

    if (!originalTool) {
      throw new NotFoundException("Tool not found");
    }

    // Check quota
    await this.billingService.checkQuota(organizationId, "tools", 1);

    const duplicatedTool = await this.prisma.tool.create({
      data: {
        name: `${originalTool.name} (Copy)`,
        description: originalTool.description,
        schema: originalTool.schema,
        endpoint: originalTool.endpoint,
        method: originalTool.method,
        headers: originalTool.headers,
        authentication: originalTool.authentication,
        status: ToolStatus.DRAFT,
        isPublic: false,
        tags: originalTool.tags,
        category: originalTool.category,
        metadata: originalTool.metadata,
        userId,
        organizationId,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Track usage
    await this.billingService.trackUsage(organizationId, "tools", 1, 0, {
      toolId: duplicatedTool.id,
      action: "duplicate",
      originalToolId: id,
    });

    // Track analytics
    await this.analyticsService.track("tool_duplicated", {
      toolId: duplicatedTool.id,
      originalToolId: id,
      organizationId,
      userId,
    });

    return duplicatedTool as Tool;
  }

  async getStats(organizationId: string) {
    const [byStatus, totalExecutions, recentExecutions] = await Promise.all([
      this.prisma.tool.groupBy({
        by: ["status"],
        where: { organizationId },
        _count: {
          status: true,
        },
      }),
      this.prisma.toolExecution.count({
        where: {
          tool: {
            organizationId,
          },
        },
      }),
      this.prisma.toolExecution.count({
        where: {
          tool: {
            organizationId,
          },
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      }),
    ]);

    const statusCounts = byStatus.reduce((acc, item) => {
      acc[item.status] = item._count.status;
      return acc;
    }, {});

    return {
      byStatus: statusCounts,
      totalExecutions,
      recentExecutions,
    };
  }

  private async executeToolLogic(
    tool: any,
    input: Record<string, any>,
    executionId: string,
  ): Promise<any> {
    const { schema, endpoint, method, headers, authentication, category } =
      tool;

    // Validate input against schema
    this.validateInput(input, schema);

    // Handle different tool categories
    switch (category) {
      case "api":
        return await this.executeApiTool(tool, input);
      case "database":
        return await this.executeDatabaseTool(tool, input);
      case "file":
        return await this.executeFileTool(tool, input);
      case "email":
        return await this.executeEmailTool(tool, input);
      case "webhook":
        return await this.executeWebhookTool(tool, input);
      case "custom":
        return await this.executeCustomTool(tool, input);
      default:
        return await this.executeGenericTool(tool, input);
    }
  }

  private validateInput(input: Record<string, any>, schema: any): void {
    if (!schema || !schema.properties) return;

    const required = schema.required || [];

    // Check required fields
    for (const field of required) {
      if (
        !(field in input) ||
        input[field] === null ||
        input[field] === undefined
      ) {
        throw new BadRequestException(`Required field '${field}' is missing`);
      }
    }

    // Validate field types
    for (const [field, value] of Object.entries(input)) {
      const fieldSchema = schema.properties[field];
      if (fieldSchema && !this.validateFieldType(value, fieldSchema)) {
        throw new BadRequestException(`Field '${field}' has invalid type`);
      }
    }
  }

  private validateFieldType(value: any, fieldSchema: any): boolean {
    const { type, format } = fieldSchema;

    switch (type) {
      case "string":
        if (typeof value !== "string") return false;
        if (format === "email" && !this.isValidEmail(value)) return false;
        if (format === "url" && !this.isValidUrl(value)) return false;
        break;
      case "number":
      case "integer":
        if (typeof value !== "number") return false;
        if (type === "integer" && !Number.isInteger(value)) return false;
        break;
      case "boolean":
        if (typeof value !== "boolean") return false;
        break;
      case "array":
        if (!Array.isArray(value)) return false;
        break;
      case "object":
        if (typeof value !== "object" || Array.isArray(value)) return false;
        break;
    }

    return true;
  }

  private async executeApiTool(
    tool: any,
    input: Record<string, any>,
  ): Promise<any> {
    const { endpoint, method, headers, authentication } = tool;

    if (!endpoint) {
      throw new BadRequestException("API tool requires an endpoint");
    }

    // Build headers
    const requestHeaders: Record<string, string> = {
      "Content-Type": "application/json",
      "User-Agent": "SynapseAI-Tool/1.0",
      ...headers,
    };

    // Add authentication
    if (authentication) {
      this.addAuthentication(requestHeaders, authentication);
    }

    // Build URL with query parameters for GET requests
    let url = endpoint;
    let body: string | undefined;

    if (method?.toLowerCase() === "get") {
      const params = new URLSearchParams();
      Object.entries(input).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          params.append(key, String(value));
        }
      });
      url += `?${params.toString()}`;
    } else {
      body = JSON.stringify(input);
    }

    // Execute request with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const response = await fetch(url, {
        method: method || "POST",
        headers: requestHeaders,
        body,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `HTTP ${response.status}: ${errorText || response.statusText}`,
        );
      }

      const contentType = response.headers.get("content-type");
      let result;

      if (contentType?.includes("application/json")) {
        result = await response.json();
      } else {
        result = { data: await response.text() };
      }

      return {
        success: true,
        data: result,
        metadata: {
          status: response.status,
          headers: Object.fromEntries(response.headers.entries()),
          contentType,
        },
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === "AbortError") {
        throw new Error("Request timeout after 30 seconds");
      }

      throw error;
    }
  }

  private async executeDatabaseTool(
    tool: any,
    input: Record<string, any>,
  ): Promise<any> {
    // Database tools would connect to various databases
    // This is a simplified implementation
    const { authentication, metadata } = tool;
    const { query, parameters } = input;

    if (!query) {
      throw new BadRequestException("Database tool requires a query");
    }

    // Validate query for safety (prevent SQL injection)
    this.validateDatabaseQuery(query);

    return {
      success: true,
      data: {
        query,
        parameters,
        result: "Database query executed successfully",
        rowsAffected: 0,
      },
      metadata: {
        database: metadata?.database || "unknown",
        executionTime: Date.now(),
      },
    };
  }

  private async executeFileTool(
    tool: any,
    input: Record<string, any>,
  ): Promise<any> {
    const { operation, filePath, content, encoding } = input;

    switch (operation) {
      case "read":
        return {
          success: true,
          data: {
            content: "File content would be here",
            size: 1024,
            encoding: encoding || "utf8",
          },
        };

      case "write":
        return {
          success: true,
          data: {
            bytesWritten: content?.length || 0,
            filePath,
          },
        };

      case "delete":
        return {
          success: true,
          data: {
            deleted: true,
            filePath,
          },
        };

      default:
        throw new BadRequestException(
          `Unsupported file operation: ${operation}`,
        );
    }
  }

  private async executeEmailTool(
    tool: any,
    input: Record<string, any>,
  ): Promise<any> {
    const { to, subject, body, attachments } = input;

    if (!to || !subject || !body) {
      throw new BadRequestException(
        "Email tool requires to, subject, and body",
      );
    }

    // Validate email addresses
    const recipients = Array.isArray(to) ? to : [to];
    for (const email of recipients) {
      if (!this.isValidEmail(email)) {
        throw new BadRequestException(`Invalid email address: ${email}`);
      }
    }

    return {
      success: true,
      data: {
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        recipients: recipients.length,
        subject,
        attachments: attachments?.length || 0,
        sentAt: new Date().toISOString(),
      },
    };
  }

  private async executeWebhookTool(
    tool: any,
    input: Record<string, any>,
  ): Promise<any> {
    const { url, method, headers, payload } = input;

    if (!url) {
      throw new BadRequestException("Webhook tool requires a URL");
    }

    if (!this.isValidUrl(url)) {
      throw new BadRequestException("Invalid webhook URL");
    }

    return await this.executeApiTool(
      {
        ...tool,
        endpoint: url,
        method: method || "POST",
        headers: headers || {},
      },
      payload || input,
    );
  }

  private async executeCustomTool(
    tool: any,
    input: Record<string, any>,
  ): Promise<any> {
    // Custom tools would have their own execution logic
    // This could involve running sandboxed code, calling specific APIs, etc.
    const { code, runtime } = tool.metadata || {};

    if (!code) {
      throw new BadRequestException("Custom tool requires code to execute");
    }

    // For security, custom code execution would be sandboxed
    return {
      success: true,
      data: {
        input,
        output: "Custom tool executed successfully",
        runtime: runtime || "javascript",
      },
    };
  }

  private async executeGenericTool(
    tool: any,
    input: Record<string, any>,
  ): Promise<any> {
    // Fallback for tools that don't fit other categories
    if (tool.endpoint) {
      return await this.executeApiTool(tool, input);
    }

    return {
      success: true,
      data: {
        processed: true,
        input,
        timestamp: new Date().toISOString(),
        toolId: tool.id,
      },
    };
  }

  private addAuthentication(headers: Record<string, string>, auth: any): void {
    switch (auth.type) {
      case "bearer":
        headers["Authorization"] = `Bearer ${auth.token}`;
        break;
      case "basic":
        const credentials = Buffer.from(
          `${auth.username}:${auth.password}`,
        ).toString("base64");
        headers["Authorization"] = `Basic ${credentials}`;
        break;
      case "api_key":
        if (auth.location === "header") {
          headers[auth.name] = auth.value;
        }
        break;
    }
  }

  private validateDatabaseQuery(query: string): void {
    // Basic SQL injection prevention
    const dangerousPatterns = [
      /drop\s+table/i,
      /delete\s+from/i,
      /truncate\s+table/i,
      /alter\s+table/i,
      /create\s+table/i,
      /insert\s+into/i,
      /update\s+.*set/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(query)) {
        throw new BadRequestException(
          "Potentially dangerous SQL query detected",
        );
      }
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private calculateExecutionCost(
    tool: any,
    duration: number,
    dataProcessed: number = 0,
  ): number {
    // Base cost calculation based on tool category and complexity
    let baseCost = 0.001; // $0.001 per execution

    // Adjust base cost by category
    switch (tool.category) {
      case "database":
        baseCost = 0.005; // Database operations are more expensive
        break;
      case "email":
        baseCost = 0.01; // Email sending costs
        break;
      case "file":
        baseCost = 0.002; // File operations
        break;
      case "custom":
        baseCost = 0.01; // Custom code execution
        break;
      default:
        baseCost = 0.001; // API calls and others
    }

    // Duration-based cost
    const durationCost = (duration / 1000) * 0.0001; // $0.0001 per second

    // Data processing cost (for large payloads)
    const dataCost = (dataProcessed / 1024) * 0.00001; // $0.00001 per KB

    return baseCost + durationCost + dataCost;
  }
}