import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApixService } from '../apix/apix.service';
import { AnalyticsService } from '../analytics/analytics.service';
import { MessageRole, SessionStatus } from '@prisma/client';

@Injectable()
export class SessionsService {
  private readonly logger = new Logger(SessionsService.name);

  constructor(
    private prisma: PrismaService,
    private apixService: ApixService,
    private analyticsService: AnalyticsService,
  ) {}

  async create(data: {
    title?: string;
    context?: Record<string, any>;
    agentId?: string;
    hybridId?: string;
    workflowId?: string;
    userId: string;
    organizationId: string;
  }) {
    try {
      const session = await this.prisma.session.create({
        data: {
          title: data.title || `Session ${new Date().toISOString()}`,
          context: data.context || {},
          memory: {},
          status: SessionStatus.ACTIVE,
          userId: data.userId,
          organizationId: data.organizationId,
          agentId: data.agentId,
          hybridId: data.hybridId,
          workflowId: data.workflowId,
        },
      });

      // Track analytics
      await this.analyticsService.track('session_created', {
        sessionId: session.id,
        userId: data.userId,
        organizationId: data.organizationId,
        hasAgent: !!data.agentId,
        hasHybrid: !!data.hybridId,
        hasWorkflow: !!data.workflowId,
      });

      // Emit session created event
      await this.apixService.emitSessionEvent(
        'session_created',
        session.id,
        data.organizationId,
        data.userId,
        {
          title: session.title,
          agentId: data.agentId,
          hybridId: data.hybridId,
          workflowId: data.workflowId,
        },
      );

      return session;
    } catch (error) {
      this.logger.error(`Error creating session: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findAll(organizationId: string, options?: {
    userId?: string;
    status?: SessionStatus;
    agentId?: string;
    hybridId?: string;
    workflowId?: string;
    limit?: number;
    offset?: number;
  }) {
    const { userId, status, agentId, hybridId, workflowId, limit = 20, offset = 0 } = options || {};

    const where = {
      organizationId,
      ...(userId && { userId }),
      ...(status && { status }),
      ...(agentId && { agentId }),
      ...(hybridId && { hybridId }),
      ...(workflowId && { workflowId }),
    };

    const [sessions, total] = await Promise.all([
      this.prisma.session.findMany({
        where,
        orderBy: { updatedAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          messages: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      }),
      this.prisma.session.count({ where }),
    ]);

    return {
      data: sessions,
      meta: {
        total,
        limit,
        offset,
      },
    };
  }

  async findOne(id: string) {
    const session = await this.prisma.session.findUnique({
      where: { id },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' },
        },
      },
    });

    if (!session) {
      throw new NotFoundException(`Session with ID ${id} not found`);
    }

    return session;
  }

  async update(id: string, data: {
    title?: string;
    context?: Record<string, any>;
    memory?: Record<string, any>;
    status?: SessionStatus;
    metadata?: Record<string, any>;
  }) {
    const session = await this.prisma.session.update({
      where: { id },
      data,
    });

    // Emit session updated event
    await this.apixService.emitSessionEvent(
      'session_updated',
      session.id,
      session.organizationId,
      session.userId,
      {
        title: session.title,
        status: session.status,
      },
    );

    return session;
  }

  async addMessage(data: {
    sessionId: string;
    content: string;
    role: MessageRole;
    metadata?: Record<string, any>;
  }) {
    const { sessionId, content, role, metadata } = data;

    // Get session to check if it exists and get organizationId and userId
    const session = await this.prisma.session.findUnique({
      where: { id: sessionId },
      select: { id: true, organizationId: true, userId: true },
    });

    if (!session) {
      throw new NotFoundException(`Session with ID ${sessionId} not found`);
    }

    // Create message
    const message = await this.prisma.message.create({
      data: {
        content,
        role,
        metadata: metadata || {},
        sessionId,
      },
    });

    // Emit session message event
    await this.apixService.emitSessionEvent(
      'session_message',
      sessionId,
      session.organizationId,
      session.userId,
      {
        messageId: message.id,
        content,
        role,
        timestamp: message.createdAt.toISOString(),
      },
    );

    // Track analytics
    await this.analyticsService.track('session_message', {
      sessionId,
      messageId: message.id,
      role,
      contentLength: content.length,
      userId: session.userId,
      organizationId: session.organizationId,
    });

    return message;
  }

  async getMessages(sessionId: string, options?: {
    limit?: number;
    offset?: number;
    role?: MessageRole;
  }) {
    const { limit = 50, offset = 0, role } = options || {};

    const where = {
      sessionId,
      ...(role && { role }),
    };

    const [messages, total] = await Promise.all([
      this.prisma.message.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      this.prisma.message.count({ where }),
    ]);

    return {
      data: messages,
      meta: {
        total,
        limit,
        offset,
      },
    };
  }

  async completeSession(id: string) {
    const session = await this.prisma.session.update({
      where: { id },
      data: {
        status: SessionStatus.COMPLETED,
        updatedAt: new Date(),
      },
    });

    // Emit session completed event
    await this.apixService.emitSessionEvent(
      'session_completed',
      session.id,
      session.organizationId,
      session.userId,
      {
        title: session.title,
      },
    );

    return session;
  }

  async delete(id: string) {
    // Get session first to get organizationId and userId
    const session = await this.prisma.session.findUnique({
      where: { id },
      select: { id: true, organizationId: true, userId: true },
    });

    if (!session) {
      throw new NotFoundException(`Session with ID ${id} not found`);
    }

    // Delete session
    await this.prisma.session.delete({
      where: { id },
    });

    // Track analytics
    await this.analyticsService.track('session_deleted', {
      sessionId: id,
      userId: session.userId,
      organizationId: session.organizationId,
    });

    return { success: true };
  }
}