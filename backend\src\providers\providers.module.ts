import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ProvidersService } from "./providers.service";
import { ProvidersController } from "./providers.controller";
import { PrismaModule } from "../prisma/prisma.module";
import { BillingModule } from "../billing/billing.module";
import { AnalyticsModule } from "../analytics/analytics.module";
import { ApixModule } from "../apix/apix.module";

@Module({
  imports: [PrismaModule, BillingModule, AnalyticsModule, ApixModule],
  controllers: [ProvidersController],
  providers: [ProvidersService],
  exports: [ProvidersService],
})
export class ProvidersModule {}
