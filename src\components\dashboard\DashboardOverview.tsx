"use client";

import React, { useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  PlusCircle,
  Zap,
  TrendingUp,
  AlertCircle,
  Users,
  BarChart3,
} from "lucide-react";
import { apiClient } from "@/lib/api";
import { useApi } from "@/hooks/useApi";

interface UsageStatistic {
  title: string;
  value: string;
  change: string;
  isPositive: boolean;
  icon: React.ReactNode;
}

interface QuotaUsage {
  name: string;
  used: number;
  total: number;
  percentage: number;
}

interface DashboardOverviewProps {
  usageStats?: UsageStatistic[];
  quotaUsage?: QuotaUsage[];
  recentExecutions?: number;
}

const DashboardOverview = ({
  usageStats: propUsageStats,
  quotaUsage: propQuotaUsage,
  recentExecutions: propRecentExecutions,
}: DashboardOverviewProps) => {
  // Fetch real agent statistics
  const {
    data: agentStatsResponse,
    loading: statsLoading,
    error: statsError,
  } = useApi(() => apiClient.getAgentStats(), { immediate: true });

  const agentStats = agentStatsResponse?.data;

  // Use real data if available, fallback to props or defaults
  const usageStats = propUsageStats || [
    {
      title: "Total Executions",
      value: agentStats?.totalExecutions?.toLocaleString() || "0",
      change: "+12.5%",
      isPositive: true,
      icon: <Zap className="h-4 w-4 text-blue-500" />,
    },
    {
      title: "Active Agents",
      value: agentStats?.byStatus?.ACTIVE?.toString() || "0",
      change: "+3",
      isPositive: true,
      icon: <Users className="h-4 w-4 text-green-500" />,
    },
    {
      title: "Recent Executions",
      value: agentStats?.recentExecutions?.toLocaleString() || "0",
      change: "+2.1%",
      isPositive: true,
      icon: <TrendingUp className="h-4 w-4 text-emerald-500" />,
    },
    {
      title: "Draft Agents",
      value: agentStats?.byStatus?.DRAFT?.toString() || "0",
      change: "-0.5%",
      isPositive: true,
      icon: <AlertCircle className="h-4 w-4 text-yellow-500" />,
    },
  ];

  const quotaUsage = propQuotaUsage || [
    {
      name: "API Calls",
      used: 8750,
      total: 10000,
      percentage: 87.5,
    },
    {
      name: "Storage",
      used: 4.2,
      total: 10,
      percentage: 42,
    },
    {
      name: "Agent Executions",
      used: agentStats?.totalExecutions || 0,
      total: 5000,
      percentage: ((agentStats?.totalExecutions || 0) / 5000) * 100,
    },
  ];

  const recentExecutions =
    propRecentExecutions || agentStats?.recentExecutions || 0;
  return (
    <div className="bg-background w-full p-6 rounded-lg space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Dashboard Overview
          </h2>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your AI platform.
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">View Reports</Button>
          <Button>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create New
          </Button>
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {usageStats.map((stat, index) => (
          <Card key={index} className="bg-card">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              {stat.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p
                className={`text-xs ${stat.isPositive ? "text-green-500" : "text-red-500"}`}
              >
                {stat.change} from last month
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Access and Quota Usage */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Quick Access */}
        <Card className="lg:col-span-1 bg-card">
          <CardHeader>
            <CardTitle>Quick Access</CardTitle>
            <CardDescription>
              Create new resources or access recent items
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full justify-start" variant="outline">
              <PlusCircle className="mr-2 h-4 w-4" />
              Create New Agent
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <PlusCircle className="mr-2 h-4 w-4" />
              Create New Tool
            </Button>
            <Button className="w-full justify-start" variant="outline">
              <PlusCircle className="mr-2 h-4 w-4" />
              Create New Workflow
            </Button>
          </CardContent>
          <CardFooter>
            <Button variant="ghost" className="w-full">
              View All Resources
            </Button>
          </CardFooter>
        </Card>

        {/* Quota Usage */}
        <Card className="lg:col-span-2 bg-card">
          <CardHeader>
            <CardTitle>Resource Usage</CardTitle>
            <CardDescription>
              Current quota usage across your organization
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {quotaUsage.map((quota, index) => (
              <div key={index} className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{quota.name}</span>
                  <span className="text-sm text-muted-foreground">
                    {typeof quota.used === "number" &&
                    Number.isInteger(quota.used)
                      ? quota.used.toLocaleString()
                      : quota.used}
                    {" / "}
                    {typeof quota.total === "number" &&
                    Number.isInteger(quota.total)
                      ? quota.total.toLocaleString()
                      : quota.total}
                    {!Number.isInteger(quota.used) && " GB"}
                  </span>
                </div>
                <Progress value={quota.percentage} className="h-2" />
              </div>
            ))}
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">
              <BarChart3 className="mr-2 h-4 w-4" />
              View Detailed Analytics
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Recent Activity Summary */}
      <Card className="bg-card">
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            You have {recentExecutions} executions in the last 24 hours
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[100px] flex items-center justify-center border rounded-md">
            <p className="text-muted-foreground">
              Activity chart will be displayed here
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="ghost">View All Activity</Button>
          <Button variant="outline">Export Report</Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default DashboardOverview;
