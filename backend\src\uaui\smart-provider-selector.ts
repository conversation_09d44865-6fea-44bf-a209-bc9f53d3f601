import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { ProvidersService } from "../providers/providers.service";
import { AnalyticsService } from "../analytics/analytics.service";
import { ProviderType, ProviderStatus } from "@prisma/client";

export interface ProviderSelectionCriteria {
  model?: string;
  task?: string;
  maxLatency?: number;
  minReliability?: number;
  maxCost?: number;
  priority?: number;
  capabilities?: string[];
  context?: Record<string, any>;
}

@Injectable()
export class SmartProviderSelector {
  private readonly logger = new Logger(SmartProviderSelector.name);

  constructor(
    private prisma: PrismaService,
    private providersService: ProvidersService,
    private analyticsService: AnalyticsService
  ) {}

  async selectProvider(
    organizationId: string,
    criteria: ProviderSelectionCriteria
  ) {
    this.logger.log(
      `Selecting optimal provider for org ${organizationId} with criteria: ${JSON.stringify(
        criteria
      )}`
    );

    try {
      // Get all active providers for the organization
      const providers = await this.prisma.provider.findMany({
        where: {
          organizationId,
          status: ProviderStatus.ACTIVE,
          isActive: true,
        },
      });

      if (providers.length === 0) {
        throw new Error("No active providers available");
      }

      // Filter providers by model if specified
      let eligibleProviders = providers;
      if (criteria.model) {
        eligibleProviders = providers.filter((provider) => {
          const providerModels = provider.metadata?.models || [];
          return providerModels.includes(criteria.model);
        });

        if (eligibleProviders.length === 0) {
          this.logger.warn(
            `No providers support model ${criteria.model}, falling back to all providers`
          );
          eligibleProviders = providers;
        }
      }

      // Filter by capabilities if specified
      if (criteria.capabilities && criteria.capabilities.length > 0) {
        eligibleProviders = eligibleProviders.filter((provider) => {
          const providerCapabilities = provider.metadata?.capabilities || [];
          return criteria.capabilities.every((cap) =>
            providerCapabilities.includes(cap)
          );
        });

        if (eligibleProviders.length === 0) {
          this.logger.warn(
            `No providers support all required capabilities, falling back to all providers`
          );
          eligibleProviders = providers;
        }
      }

      // Score providers based on criteria
      const scoredProviders = eligibleProviders.map((provider) => {
        let score = 0;

        // Base score from provider priority
        score += provider.priority * 10;

        // Adjust score based on reliability
        if (criteria.minReliability) {
          const reliabilityScore =
            provider.metadata?.reliability || 0.5;
          if (reliabilityScore >= criteria.minReliability) {
            score += reliabilityScore * 20;
          } else {
            score -= 30;
          }
        }

        // Adjust score based on latency
        if (criteria.maxLatency) {
          const latency = provider.metadata?.averageLatency || 1000;
          if (latency <= criteria.maxLatency) {
            score += 15;
          } else {
            score -= (latency - criteria.maxLatency) / 100;
          }
        }

        // Adjust score based on cost
        if (criteria.maxCost) {
          const costPerToken = provider.costPerToken || 0.0001;
          if (costPerToken <= criteria.maxCost) {
            score += 10;
          } else {
            score -= 20;
          }
        }

        // Bonus for recently used providers (cache warmth)
        if (provider.metadata?.lastUsedAt) {
          const lastUsed = new Date(provider.metadata.lastUsedAt);
          const now = new Date();
          const hoursSinceLastUse =
            (now.getTime() - lastUsed.getTime()) / (1000 * 60 * 60);
          if (hoursSinceLastUse < 1) {
            score += 5;
          }
        }

        return {
          provider,
          score,
        };
      });

      // Sort by score (descending)
      scoredProviders.sort((a, b) => b.score - a.score);

      // Track analytics
      await this.analyticsService.track("provider_selection", {
        organizationId,
        selectedProviderId: scoredProviders[0]?.provider.id,
        criteria,
        scoreDetails: scoredProviders.map((sp) => ({
          providerId: sp.provider.id,
          providerName: sp.provider.name,
          score: sp.score,
        })),
      });

      // Return the highest scoring provider
      if (scoredProviders.length > 0) {
        return scoredProviders[0].provider;
      }

      throw new Error("No suitable provider found");
    } catch (error) {
      this.logger.error(
        `Error selecting provider: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  async getFallbackProvider(
    organizationId: string,
    excludeProviderId?: string
  ) {
    // Get a fallback provider, excluding the one that failed
    const fallbackProvider = await this.prisma.provider.findFirst({
      where: {
        organizationId,
        status: ProviderStatus.ACTIVE,
        isActive: true,
        id: excludeProviderId ? { not: excludeProviderId } : undefined,
      },
      orderBy: {
        priority: "desc",
      },
    });

    if (!fallbackProvider) {
      throw new Error("No fallback provider available");
    }

    return fallbackProvider;
  }

  async getProviderByType(
    organizationId: string,
    type: ProviderType
  ) {
    // Get a provider of specific type
    const provider = await this.prisma.provider.findFirst({
      where: {
        organizationId,
        type,
        status: ProviderStatus.ACTIVE,
        isActive: true,
      },
      orderBy: {
        priority: "desc",
      },
    });

    if (!provider) {
      throw new Error(`No active provider of type ${type} available`);
    }

    return provider;
  }
}