import { Controller, Get } from "@nestjs/common";
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from "@nestjs/terminus";
import { PrismaService } from "../prisma/prisma.service";

@Controller("health")
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private prisma: PrismaService,
  ) {}

  @Get()
  @HealthCheck()
  check() {
    return this.health.check([
      // Database health check
      async () => {
        const isHealthy = await this.prisma.healthCheck();
        return {
          database: {
            status: isHealthy ? "up" : "down",
          },
        };
      },
      // Memory health check (heap should not use more than 300MB)
      () => this.memory.checkHeap("memory_heap", 300 * 1024 * 1024),
      // Memory health check (RSS should not use more than 300MB)
      () => this.memory.checkRSS("memory_rss", 300 * 1024 * 1024),
      // Disk health check (disk should not use more than 80% of available space)
      () =>
        this.disk.checkStorage("storage", {
          path: "/",
          thresholdPercent: 0.8,
        }),
    ]);
  }

  @Get("ready")
  ready() {
    return {
      status: "ready",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  @Get("live")
  live() {
    return {
      status: "alive",
      timestamp: new Date().toISOString(),
      pid: process.pid,
    };
  }
}
