import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AnalyticsService } from '../analytics/analytics.service';
import { NotificationChannel, NotificationPriority, NotificationStatus, NotificationType } from '@prisma/client';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    private prisma: PrismaService,
    private analyticsService: AnalyticsService,
  ) {}

  async create(data: {
    title: string;
    message: string;
    type: NotificationType;
    channel: NotificationChannel;
    priority?: NotificationPriority;
    data?: Record<string, any>;
    userId: string;
    organizationId: string;
  }) {
    try {
      const notification = await this.prisma.notification.create({
        data: {
          title: data.title,
          message: data.message,
          type: data.type,
          channel: data.channel,
          priority: data.priority || NotificationPriority.MEDIUM,
          status: NotificationStatus.PENDING,
          data: data.data || {},
          userId: data.userId,
          organizationId: data.organizationId,
        },
      });

      // Track analytics
      await this.analyticsService.track('notification_created', {
        notificationId: notification.id,
        type: notification.type,
        channel: notification.channel,
        priority: notification.priority,
        userId: data.userId,
        organizationId: data.organizationId,
      });

      // Process notification (in a real implementation, this would be handled by a queue)
      await this.processNotification(notification.id);

      return notification;
    } catch (error) {
      this.logger.error(`Error creating notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findAll(organizationId: string, options?: {
    userId?: string;
    status?: NotificationStatus;
    type?: NotificationType;
    channel?: NotificationChannel;
    priority?: NotificationPriority;
    limit?: number;
    offset?: number;
  }) {
    const { userId, status, type, channel, priority, limit = 20, offset = 0 } = options || {};

    const where = {
      organizationId,
      ...(userId && { userId }),
      ...(status && { status }),
      ...(type && { type }),
      ...(channel && { channel }),
      ...(priority && { priority }),
    };

    const [notifications, total] = await Promise.all([
      this.prisma.notification.findMany({
        where,
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' },
        ],
        take: limit,
        skip: offset,
      }),
      this.prisma.notification.count({ where }),
    ]);

    return {
      data: notifications,
      meta: {
        total,
        limit,
        offset,
      },
    };
  }

  async findOne(id: string) {
    return this.prisma.notification.findUnique({
      where: { id },
    });
  }

  async markAsRead(id: string) {
    return this.prisma.notification.update({
      where: { id },
      data: {
        status: NotificationStatus.DELIVERED,
        deliveredAt: new Date(),
      },
    });
  }

  async getUserPreferences(userId: string, organizationId: string) {
    return this.prisma.notificationPreference.findMany({
      where: {
        userId,
        organizationId,
      },
    });
  }

  async updateUserPreferences(
    userId: string,
    organizationId: string,
    preferences: Array<{
      type: NotificationType;
      channel: NotificationChannel;
      enabled: boolean;
      settings?: Record<string, any>;
    }>,
  ) {
    const results = [];

    for (const pref of preferences) {
      const result = await this.prisma.notificationPreference.upsert({
        where: {
          userId_type_channel: {
            userId,
            type: pref.type,
            channel: pref.channel,
          },
        },
        update: {
          enabled: pref.enabled,
          settings: pref.settings || {},
        },
        create: {
          userId,
          organizationId,
          type: pref.type,
          channel: pref.channel,
          enabled: pref.enabled,
          settings: pref.settings || {},
        },
      });

      results.push(result);
    }

    return results;
  }

  private async processNotification(id: string) {
    try {
      const notification = await this.prisma.notification.findUnique({
        where: { id },
        include: {
          user: true,
        },
      });

      if (!notification) {
        this.logger.warn(`Notification ${id} not found for processing`);
        return;
      }

      // Check if user has opted out of this notification type/channel
      const preference = await this.prisma.notificationPreference.findUnique({
        where: {
          userId_type_channel: {
            userId: notification.userId,
            type: notification.type,
            channel: notification.channel,
          },
        },
      });

      if (preference && !preference.enabled) {
        // User has opted out, mark as delivered without sending
        await this.prisma.notification.update({
          where: { id },
          data: {
            status: NotificationStatus.DELIVERED,
            deliveredAt: new Date(),
          },
        });
        return;
      }

      // Process based on channel
      let success = false;
      let error = null;

      try {
        switch (notification.channel) {
          case NotificationChannel.EMAIL:
            success = await this.sendEmail(notification);
            break;
          case NotificationChannel.SMS:
            success = await this.sendSMS(notification);
            break;
          case NotificationChannel.PUSH:
            success = await this.sendPushNotification(notification);
            break;
          case NotificationChannel.WEBHOOK:
            success = await this.sendWebhook(notification);
            break;
          case NotificationChannel.SLACK:
            success = await this.sendSlack(notification);
            break;
          case NotificationChannel.TEAMS:
            success = await this.sendTeams(notification);
            break;
          default:
            this.logger.warn(`Unsupported notification channel: ${notification.channel}`);
            success = false;
            error = `Unsupported channel: ${notification.channel}`;
        }
      } catch (err) {
        success = false;
        error = err.message;
        this.logger.error(`Error sending notification: ${err.message}`, err.stack);
      }

      // Update notification status
      await this.prisma.notification.update({
        where: { id },
        data: {
          status: success ? NotificationStatus.SENT : NotificationStatus.FAILED,
          sentAt: success ? new Date() : null,
          retryCount: notification.retryCount + 1,
          data: {
            ...notification.data,
            lastError: error,
            lastAttempt: new Date().toISOString(),
          },
        },
      });

      // Track analytics
      await this.analyticsService.track('notification_processed', {
        notificationId: id,
        success,
        channel: notification.channel,
        error,
      });
    } catch (error) {
      this.logger.error(`Error processing notification ${id}: ${error.message}`, error.stack);
    }
  }

  // These methods would be implemented with actual email/SMS/etc. providers
  private async sendEmail(notification: any): Promise<boolean> {
    // In a real implementation, this would use an email service
    this.logger.log(`[MOCK] Sending email to ${notification.user.email}: ${notification.title}`);
    return true;
  }

  private async sendSMS(notification: any): Promise<boolean> {
    // In a real implementation, this would use an SMS service
    this.logger.log(`[MOCK] Sending SMS: ${notification.title}`);
    return true;
  }

  private async sendPushNotification(notification: any): Promise<boolean> {
    // In a real implementation, this would use a push notification service
    this.logger.log(`[MOCK] Sending push notification: ${notification.title}`);
    return true;
  }

  private async sendWebhook(notification: any): Promise<boolean> {
    // In a real implementation, this would call a webhook URL
    this.logger.log(`[MOCK] Sending webhook: ${notification.title}`);
    return true;
  }

  private async sendSlack(notification: any): Promise<boolean> {
    // In a real implementation, this would use Slack API
    this.logger.log(`[MOCK] Sending Slack message: ${notification.title}`);
    return true;
  }

  private async sendTeams(notification: any): Promise<boolean> {
    // In a real implementation, this would use Microsoft Teams API
    this.logger.log(`[MOCK] Sending Teams message: ${notification.title}`);
    return true;
  }
}