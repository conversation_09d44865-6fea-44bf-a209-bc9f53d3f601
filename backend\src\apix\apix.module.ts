import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ApixGateway } from "./apix.gateway";
import { ApixService } from "./apix.service";
import { PrismaModule } from "../prisma/prisma.module";
import { AuthModule } from "../auth/auth.module";
import { BillingModule } from "../billing/billing.module";
import { AnalyticsModule } from "../analytics/analytics.module";

@Module({
  imports: [PrismaModule, AuthModule, BillingModule, AnalyticsModule],
  providers: [ApixGateway, ApixService],
  exports: [ApixService],
})
export class ApixModule {}
