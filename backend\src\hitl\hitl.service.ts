import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ApixService } from '../apix/apix.service';
import { AnalyticsService } from '../analytics/analytics.service';
import { NotificationsService } from '../notifications/notifications.service';
import { HITLPriority, HITLRequestType, HITLStatus, HITLDecisionType } from '../../../shared/types';

@Injectable()
export class HITLService {
  private readonly logger = new Logger(HITLService.name);

  constructor(
    private prisma: PrismaService,
    private apixService: ApixService,
    private analyticsService: AnalyticsService,
    private notificationsService: NotificationsService,
  ) {}

  async createRequest(data: {
    type: HITLRequestType;
    title: string;
    description?: string;
    data?: Record<string, any>;
    priority?: HITLPriority;
    userId: string;
    sessionId?: string;
    organizationId: string;
    assigneeId?: string;
  }) {
    try {
      const request = await this.prisma.hITLRequest.create({
        data: {
          type: data.type,
          title: data.title,
          description: data.description,
          data: data.data || {},
          priority: data.priority || HITLPriority.MEDIUM,
          status: HITLStatus.PENDING,
          userId: data.userId,
          sessionId: data.sessionId,
          organizationId: data.organizationId,
          assigneeId: data.assigneeId,
        },
      });

      // Track analytics
      await this.analyticsService.track('hitl_request_created', {
        requestId: request.id,
        type: request.type,
        priority: request.priority,
        userId: data.userId,
        organizationId: data.organizationId,
        hasSession: !!data.sessionId,
        hasAssignee: !!data.assigneeId,
      });

      // Emit HITL event
      await this.apixService.emitHITLEvent(
        'hitl_request_created',
        request.id,
        data.organizationId,
        data.userId,
        {
          title: request.title,
          type: request.type,
          priority: request.priority,
          sessionId: data.sessionId,
        },
      );

      // Notify assignee if specified
      if (data.assigneeId) {
        await this.notificationsService.create({
          title: 'New HITL Request',
          message: `You have been assigned a new ${data.type} request: ${data.title}`,
          type: 'INFO',
          channel: 'EMAIL',
          userId: data.assigneeId,
          organizationId: data.organizationId,
          data: {
            requestId: request.id,
            type: request.type,
            priority: request.priority,
          },
        });
      } else {
        // Notify all admins in the organization
        const admins = await this.prisma.user.findMany({
          where: {
            organizationId: data.organizationId,
            role: {
              in: ['SUPER_ADMIN', 'ORG_ADMIN'],
            },
          },
        });

        for (const admin of admins) {
          await this.notificationsService.create({
            title: 'New HITL Request',
            message: `A new ${data.type} request requires attention: ${data.title}`,
            type: 'INFO',
            channel: 'EMAIL',
            userId: admin.id,
            organizationId: data.organizationId,
            data: {
              requestId: request.id,
              type: request.type,
              priority: request.priority,
            },
          });
        }
      }

      return request;
    } catch (error) {
      this.logger.error(`Error creating HITL request: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async findAll(organizationId: string, options?: {
    status?: HITLStatus;
    type?: HITLRequestType;
    priority?: HITLPriority;
    userId?: string;
    assigneeId?: string;
    sessionId?: string;
    limit?: number;
    offset?: number;
  }) {
    const { status, type, priority, userId, assigneeId, sessionId, limit = 20, offset = 0 } = options || {};

    const where = {
      organizationId,
      ...(status && { status }),
      ...(type && { type }),
      ...(priority && { priority }),
      ...(userId && { userId }),
      ...(assigneeId && { assigneeId }),
      ...(sessionId && { sessionId }),
    };

    const [requests, total] = await Promise.all([
      this.prisma.hITLRequest.findMany({
        where,
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' },
        ],
        take: limit,
        skip: offset,
        include: {
          decisions: {
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      }),
      this.prisma.hITLRequest.count({ where }),
    ]);

    return {
      data: requests,
      meta: {
        total,
        limit,
        offset,
      },
    };
  }

  async findOne(id: string) {
    const request = await this.prisma.hITLRequest.findUnique({
      where: { id },
      include: {
        decisions: {
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
      },
    });

    if (!request) {
      throw new NotFoundException(`HITL request with ID ${id} not found`);
    }

    return request;
  }

  async update(id: string, data: {
    title?: string;
    description?: string;
    data?: Record<string, any>;
    priority?: HITLPriority;
    status?: HITLStatus;
    assigneeId?: string;
  }) {
    const request = await this.prisma.hITLRequest.update({
      where: { id },
      data,
    });

    // Track analytics
    await this.analyticsService.track('hitl_request_updated', {
      requestId: id,
      status: data.status,
      priority: data.priority,
      hasAssignee: !!data.assigneeId,
    });

    return request;
  }

  async makeDecision(id: string, data: {
    decision: HITLDecisionType;
    reasoning?: string;
    data?: Record<string, any>;
    userId: string;
  }) {
    // Get the request first
    const request = await this.findOne(id);

    // Create decision
    const decision = await this.prisma.hITLDecision.create({
      data: {
        requestId: id,
        decision: data.decision,
        reasoning: data.reasoning,
        data: data.data || {},
        userId: data.userId,
      },
    });

    // Update request status based on decision
    let newStatus: HITLStatus;
    switch (data.decision) {
      case HITLDecisionType.APPROVE:
        newStatus = HITLStatus.APPROVED;
        break;
      case HITLDecisionType.REJECT:
        newStatus = HITLStatus.REJECTED;
        break;
      case HITLDecisionType.ESCALATE:
        newStatus = HITLStatus.ESCALATED;
        break;  
      default:
        newStatus = HITLStatus.PENDING;
    }

    await this.prisma.hITLRequest.update({
      where: { id },
      data: {
        status: newStatus,
        response: data.data || {},
        responseAt: new Date(),
      },
    });

    // Emit HITL event
    await this.apixService.emitHITLEvent(
      `hitl_${data.decision.toLowerCase()}` as any,
      id,
      request.organizationId,
      data.userId,
      {
        decision: data.decision,
        reasoning: data.reasoning,
        data: data.data,
      },
    );

    // Track analytics
    await this.analyticsService.track('hitl_decision', {
      requestId: id,
      decision: data.decision,
      userId: data.userId,
      organizationId: request.organizationId,
      responseTime: new Date().getTime() - new Date(request.createdAt).getTime(),
    });

    // Notify the original requester
    await this.notificationsService.create({
      title: 'HITL Request Updated',
      message: `Your request "${request.title}" has been ${data.decision.toLowerCase()}`,
      type: 'INFO',
      channel: 'EMAIL',
      userId: request.userId,
      organizationId: request.organizationId,
      data: {
        requestId: id,
        decision: data.decision,
        reasoning: data.reasoning,
      },
    });

    return {
      decision,
      status: newStatus,
    };
  }

  async assignRequest(id: string, assigneeId: string) {
    const request = await this.prisma.hITLRequest.update({
      where: { id },
      data: {
        assigneeId,
      },
    });

    // Notify the assignee
    await this.notificationsService.create({
      title: 'HITL Request Assigned',
      message: `You have been assigned a ${request.type} request: ${request.title}`,
      type: 'INFO',
      channel: 'EMAIL',
      userId: assigneeId,
      organizationId: request.organizationId,
      data: {
        requestId: id,
        type: request.type,
        priority: request.priority,
      },
    });

    return request;
  }

  async getStats(organizationId: string) {
    const stats = await this.prisma.$transaction([
      // Count by status
      this.prisma.hITLRequest.groupBy({
        by: ['status'],
        where: { organizationId },
        _count: true,
      }),
      // Count by type
      this.prisma.hITLRequest.groupBy({
        by: ['type'],
        where: { organizationId },
        _count: true,
      }),
      // Count by priority
      this.prisma.hITLRequest.groupBy({
        by: ['priority'],
        where: { organizationId },
        _count: true,
      }),
      // Count pending requests
      this.prisma.hITLRequest.count({
        where: {
          organizationId,
          status: HITLStatus.PENDING,
        },
      }),
      // Average response time (in minutes)
      this.prisma.$queryRaw`
        SELECT AVG(EXTRACT(EPOCH FROM ("responseAt" - "createdAt")) / 60) as avg_response_time
        FROM "hitl_requests"
        WHERE "organizationId" = ${organizationId}
        AND "responseAt" IS NOT NULL
      `,
    ]);

    const [byStatus, byType, byPriority, pendingCount, avgResponseTime] = stats;

    // Format the results
    const byStatusMap = byStatus.reduce((acc: Record<string, number>, item: { status: string; _count: number }) => {
      acc[item.status] = item._count;
      return acc;
    }, {});

    const byTypeMap = byType.reduce((acc: Record<string, number>, item: { type: string; _count: number }) => {
      acc[item.type] = item._count;
      return acc;
    }, {});

    const byPriorityMap = byPriority.reduce((acc: Record<string, number>, item: { priority: string; _count: number }) => {
      acc[item.priority] = item._count;
      return acc;
    }, {});

    return {
      byStatus: byStatusMap,
      byType: byTypeMap,
      byPriority: byPriorityMap,
      pendingCount,
      avgResponseTime: avgResponseTime[0]?.avg_response_time || 0,
    };
  }
}