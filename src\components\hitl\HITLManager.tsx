import React, { useState, useEffect } from 'react';
import { useApi } from '@/hooks/useApi';
import { useAIPX } from '@/hooks/useAIPX';
import { apiClient } from '@/lib/api';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle, XCircle, AlertTriangle, ArrowUpRight, Clock, Filter, RefreshCw, User } from 'lucide-react';

// Types
interface HITLRequest {
  id: string;
  type: 'APPROVAL' | 'REVIEW' | 'CONSULTATION' | 'ESCALATION';
  title: string;
  description?: string;
  data: Record<string, any>;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'ESCALATED' | 'COMPLETED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  response?: Record<string, any>;
  responseAt?: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  assigneeId?: string;
  sessionId?: string;
  organizationId: string;
  decisions?: HITLDecision[];
}

interface HITLDecision {
  id: string;
  requestId: string;
  decision: 'APPROVED' | 'REJECTED' | 'ESCALATED' | 'MODIFIED';
  reasoning?: string;
  data: Record<string, any>;
  createdAt: string;
  userId: string;
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface HITLStats {
  byStatus: Record<string, number>;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
  pendingCount: number;
  avgResponseTime: number;
}

export function HITLManager() {
  const [activeTab, setActiveTab] = useState('pending');
  const [selectedRequest, setSelectedRequest] = useState<HITLRequest | null>(null);
  const [responseText, setResponseText] = useState('');
  const [decisionType, setDecisionType] = useState<'APPROVED' | 'REJECTED' | 'ESCALATED' | 'MODIFIED'>('APPROVED');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [filterType, setFilterType] = useState<string>('');
  const [filterPriority, setFilterPriority] = useState<string>('');
  const [isDecisionDialogOpen, setIsDecisionDialogOpen] = useState(false);
  
  const { toast } = useToast();
  const { connected } = useAIPX();

  // Fetch HITL requests
  const { data: requests, loading: loadingRequests, refetch: refetchRequests } = useApi(
    () => apiClient.get(`/api/v1/hitl/requests?status=${filterStatus}&type=${filterType}&priority=${filterPriority}`),
    { immediate: true }
  );

  // Fetch HITL stats
  const { data: stats, loading: loadingStats, refetch: refetchStats } = useApi(
    () => apiClient.get('/api/v1/hitl/stats'),
    { immediate: true }
  );

  // Handle APIX events for real-time updates
  useEffect(() => {
    if (!connected) return;

    const app = window.aipx?.registerApp('hitl-manager', 'dashboard');
    if (!app) return;

    // Subscribe to HITL channels
    app.subscribe(['hitl']);

    // Listen for HITL events
    const unsubscribe = app.onEvent('hitl_request_created', () => {
      refetchRequests();
      refetchStats();
      toast({
        title: 'New HITL Request',
        description: 'A new request requires your attention',
        variant: 'default',
      });
    });

    return () => {
      unsubscribe();
      app.disconnect();
    };
  }, [connected, refetchRequests, refetchStats, toast]);

  // Handle request selection
  const handleSelectRequest = async (request: HITLRequest) => {
    try {
      const response = await apiClient.get(`/api/v1/hitl/requests/${request.id}`);
      setSelectedRequest(response.data);
      setResponseText('');
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load request details',
        variant: 'destructive',
      });
    }
  };

  // Handle decision submission
  const handleSubmitDecision = async () => {
    if (!selectedRequest) return;

    try {
      await apiClient.post(`/api/v1/hitl/requests/${selectedRequest.id}/decision`, {
        decision: decisionType,
        reasoning: responseText,
        data: {
          response: responseText,
          timestamp: new Date().toISOString(),
        },
      });

      toast({
        title: 'Decision Submitted',
        description: `Request ${decisionType.toLowerCase()} successfully`,
        variant: 'default',
      });

      setIsDecisionDialogOpen(false);
      refetchRequests();
      refetchStats();
      setSelectedRequest(null);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit decision',
        variant: 'destructive',
      });
    }
  };

  // Handle request assignment
  const handleAssignToMe = async () => {
    if (!selectedRequest) return;

    try {
      const userId = localStorage.getItem('user_id');
      await apiClient.post(`/api/v1/hitl/requests/${selectedRequest.id}/assign`, {
        assigneeId: userId,
      });

      toast({
        title: 'Request Assigned',
        description: 'Request has been assigned to you',
        variant: 'default',
      });

      refetchRequests();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to assign request',
        variant: 'destructive',
      });
    }
  };

  // Render priority badge
  const renderPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return <Badge variant="destructive">Urgent</Badge>;
      case 'HIGH':
        return <Badge variant="destructive" className="bg-orange-500">High</Badge>;
      case 'MEDIUM':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case 'LOW':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Low</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'APPROVED':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Approved</Badge>;
      case 'REJECTED':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'ESCALATED':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800">Escalated</Badge>;
      case 'COMPLETED':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Completed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Render type badge
  const renderTypeBadge = (type: string) => {
    switch (type) {
      case 'APPROVAL':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Approval</Badge>;
      case 'REVIEW':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Review</Badge>;
      case 'CONSULTATION':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800">Consultation</Badge>;
      case 'ESCALATION':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Escalation</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">HITL Manager</h1>
          <p className="text-gray-500">Human-in-the-Loop Decision Management</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => { refetchRequests(); refetchStats(); }}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Filter Requests</DialogTitle>
                <DialogDescription>
                  Set filters to narrow down the requests list
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="status" className="text-right">Status</label>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Any status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any status</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                      <SelectItem value="APPROVED">Approved</SelectItem>
                      <SelectItem value="REJECTED">Rejected</SelectItem>
                      <SelectItem value="ESCALATED">Escalated</SelectItem>
                      <SelectItem value="COMPLETED">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="type" className="text-right">Type</label>
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Any type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any type</SelectItem>
                      <SelectItem value="APPROVAL">Approval</SelectItem>
                      <SelectItem value="REVIEW">Review</SelectItem>
                      <SelectItem value="CONSULTATION">Consultation</SelectItem>
                      <SelectItem value="ESCALATION">Escalation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <label htmlFor="priority" className="text-right">Priority</label>
                  <Select value={filterPriority} onValueChange={setFilterPriority}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Any priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any priority</SelectItem>
                      <SelectItem value="URGENT">Urgent</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="LOW">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button onClick={() => refetchRequests()}>Apply Filters</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data.pendingCount || 0}</div>
              <p className="text-xs text-muted-foreground">Awaiting decisions</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Avg. Response Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round(stats.data.avgResponseTime || 0)} min</div>
              <p className="text-xs text-muted-foreground">Time to decision</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Approval Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.data.byStatus && stats.data.byStatus.APPROVED && stats.data.byStatus.REJECTED
                  ? Math.round((stats.data.byStatus.APPROVED / (stats.data.byStatus.APPROVED + stats.data.byStatus.REJECTED)) * 100)
                  : 0}%
              </div>
              <p className="text-xs text-muted-foreground">Requests approved</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Urgent Requests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data.byPriority?.URGENT || 0}</div>
              <p className="text-xs text-muted-foreground">High priority items</p>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Requests List */}
        <div className="lg:col-span-1">
          <Card className="h-[calc(100vh-240px)]">
            <CardHeader>
              <CardTitle>Requests</CardTitle>
              <CardDescription>
                Manage human-in-the-loop requests
              </CardDescription>
              <Tabs defaultValue="pending" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="pending">Pending</TabsTrigger>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="mine">Assigned</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            <CardContent className="p-0 overflow-auto h-[calc(100%-120px)]">
              {loadingRequests ? (
                <div className="flex items-center justify-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : requests?.data?.length > 0 ? (
                <div className="divide-y">
                  {requests.data.map((request: HITLRequest) => (
                    <div
                      key={request.id}
                      className={`p-4 hover:bg-gray-50 cursor-pointer ${
                        selectedRequest?.id === request.id ? 'bg-gray-50' : ''
                      }`}
                      onClick={() => handleSelectRequest(request)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{request.title}</h3>
                        {renderPriorityBadge(request.priority)}
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        {renderTypeBadge(request.type)}
                        {renderStatusBadge(request.status)}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDate(request.createdAt)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-40 text-gray-500">
                  <p>No requests found</p>
                  <Button variant="ghost" size="sm" onClick={refetchRequests} className="mt-2">
                    Refresh
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Request Details */}
        <div className="lg:col-span-2">
          <Card className="h-[calc(100vh-240px)]">
            <CardHeader>
              <CardTitle>Request Details</CardTitle>
              <CardDescription>
                Review and respond to HITL requests
              </CardDescription>
            </CardHeader>
            <CardContent className="overflow-auto h-[calc(100%-180px)]">
              {selectedRequest ? (
                <div>
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h2 className="text-xl font-bold">{selectedRequest.title}</h2>
                      <div className="flex items-center gap-2 mt-1">
                        {renderTypeBadge(selectedRequest.type)}
                        {renderStatusBadge(selectedRequest.status)}
                        {renderPriorityBadge(selectedRequest.priority)}
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {formatDate(selectedRequest.createdAt)}
                    </div>
                  </div>

                  {selectedRequest.description && (
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Description</h3>
                      <p className="text-gray-700">{selectedRequest.description}</p>
                    </div>
                  )}

                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-gray-500 mb-1">Request Data</h3>
                    <div className="bg-gray-50 p-3 rounded-md overflow-auto max-h-40">
                      <pre className="text-xs">{JSON.stringify(selectedRequest.data, null, 2)}</pre>
                    </div>
                  </div>

                  {selectedRequest.decisions && selectedRequest.decisions.length > 0 && (
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Decision History</h3>
                      <div className="space-y-3">
                        {selectedRequest.decisions.map((decision) => (
                          <div key={decision.id} className="bg-gray-50 p-3 rounded-md">
                            <div className="flex justify-between items-start mb-2">
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className={
                                  decision.decision === 'APPROVED' ? 'bg-green-100 text-green-800' :
                                  decision.decision === 'REJECTED' ? 'bg-red-100 text-red-800' :
                                  decision.decision === 'ESCALATED' ? 'bg-purple-100 text-purple-800' :
                                  'bg-blue-100 text-blue-800'
                                }>
                                  {decision.decision}
                                </Badge>
                                <span className="text-sm text-gray-500">
                                  {formatDate(decision.createdAt)}
                                </span>
                              </div>
                              {decision.user && (
                                <div className="flex items-center gap-1 text-sm">
                                  <Avatar className="h-5 w-5">
                                    <AvatarFallback>{decision.user.firstName[0]}{decision.user.lastName[0]}</AvatarFallback>
                                  </Avatar>
                                  <span>{decision.user.firstName} {decision.user.lastName}</span>
                                </div>
                              )}
                            </div>
                            {decision.reasoning && (
                              <p className="text-sm">{decision.reasoning}</p>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {selectedRequest.response && (
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-gray-500 mb-1">Response</h3>
                      <div className="bg-gray-50 p-3 rounded-md overflow-auto max-h-40">
                        <pre className="text-xs">{JSON.stringify(selectedRequest.response, null, 2)}</pre>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500">
                  <AlertTriangle className="h-12 w-12 mb-2 text-gray-300" />
                  <p>Select a request to view details</p>
                </div>
              )}
            </CardContent>
            <CardFooter className="border-t p-4">
              {selectedRequest && selectedRequest.status === 'PENDING' && (
                <div className="flex justify-between w-full">
                  <Button variant="outline" onClick={handleAssignToMe}>
                    <User className="h-4 w-4 mr-2" />
                    Assign to me
                  </Button>
                  <div className="space-x-2">
                    <Dialog open={isDecisionDialogOpen} onOpenChange={setIsDecisionDialogOpen}>
                      <DialogTrigger asChild>
                        <Button>Make Decision</Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Make a Decision</DialogTitle>
                          <DialogDescription>
                            Review the request and provide your decision
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid grid-cols-4 items-center gap-4">
                            <label htmlFor="decision" className="text-right">Decision</label>
                            <Select value={decisionType} onValueChange={(value: any) => setDecisionType(value)}>
                              <SelectTrigger className="col-span-3">
                                <SelectValue placeholder="Select decision" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="APPROVED">Approve</SelectItem>
                                <SelectItem value="REJECTED">Reject</SelectItem>
                                <SelectItem value="ESCALATED">Escalate</SelectItem>
                                <SelectItem value="MODIFIED">Modify</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="grid grid-cols-4 items-center gap-4">
                            <label htmlFor="response" className="text-right">Response</label>
                            <Textarea
                              id="response"
                              className="col-span-3"
                              placeholder="Provide reasoning for your decision"
                              value={responseText}
                              onChange={(e) => setResponseText(e.target.value)}
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <Button variant="outline" onClick={() => setIsDecisionDialogOpen(false)}>Cancel</Button>
                          <Button onClick={handleSubmitDecision}>Submit Decision</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}