# Database Configuration
DATABASE_URL="postgresql://synapseai:password@localhost:5432/synapseai?schema=public"

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# API Configuration
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:3000

# AI Provider API Keys
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key
GROQ_API_KEY=gsk_your-groq-api-key
MISTRAL_API_KEY=your-mistral-api-key

# Stripe Configuration (for billing)
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Sentry Configuration (for error tracking)
SENTRY_DSN=https://<EMAIL>/project-id

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_TTL=60000
RATE_LIMIT_MAX=100

# Session Configuration
SESSION_SECRET=your-session-secret-change-this
SESSION_TTL=86400

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90

# Monitoring Configuration
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Security Configuration
CORS_ORIGIN=http://localhost:3000
HELMET_ENABLED=true
COMPRESSION_ENABLED=true

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_DB=1

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Development Configuration
DEBUG=false
SWAGGER_ENABLED=true
PRISMA_STUDIO_PORT=5555
