version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: synapseai-postgres
    environment:
      POSTGRES_DB: synapseai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - synapseai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: synapseai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - synapseai-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NESTJS Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: synapseai-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/synapseai
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - synapseai-network
    volumes:
      - ./backend:/app
      - /app/node_modules
    command: npm run start:dev

  # Next.js Frontend Service
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: synapseai-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3001
      - NEXT_PUBLIC_TEMPO=${NEXT_PUBLIC_TEMPO}
    depends_on:
      - backend
    networks:
      - synapseai-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    command: npm run dev

  # NGINX Load Balancer
  nginx:
    image: nginx:alpine
    container_name: synapseai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - synapseai-network

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: synapseai-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - synapseai-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: synapseai-grafana
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - synapseai-network
    depends_on:
      - prometheus

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  synapseai-network:
    driver: bridge
