import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { AgentsService } from "./agents.service";
import { AgentsController } from "./agents.controller";
import { PrismaModule } from "../prisma/prisma.module";
import { ProvidersModule } from "../providers/providers.module";
import { BillingModule } from "../billing/billing.module";
import { AnalyticsModule } from "../analytics/analytics.module";
import { ApixModule } from "../apix/apix.module";

@Module({
  imports: [
    PrismaModule,
    ProvidersModule,
    BillingModule,
    AnalyticsModule,
    ApixModule,
  ],
  controllers: [AgentsController],
  providers: [AgentsService],
  exports: [AgentsService],
})
export class AgentsModule {}
