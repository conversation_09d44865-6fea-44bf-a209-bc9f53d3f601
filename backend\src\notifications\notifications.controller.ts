import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthenticatedRequest } from '../common/middleware/tenant.middleware';
import { NotificationChannel, NotificationPriority, NotificationStatus, NotificationType } from '@prisma/client';

@Controller('api/v1/notifications')
@UseGuards(JwtAuthGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  async findAll(
    @Query('status') status: NotificationStatus,
    @Query('type') type: NotificationType,
    @Query('channel') channel: NotificationChannel,
    @Query('priority') priority: NotificationPriority,
    @Query('limit') limit: number,
    @Query('offset') offset: number,
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      const result = await this.notificationsService.findAll(
        req.user.organizationId,
        {
          userId: req.user.id,
          status,
          type,
          channel,
          priority,
          limit: limit ? Number(limit) : undefined,
          offset: offset ? Number(offset) : undefined,
        },
      );

      return {
        success: true,
        data: result.data,
        meta: result.meta,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch notifications',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    try {
      const notification = await this.notificationsService.findOne(id);

      if (!notification) {
        throw new HttpException('Notification not found', HttpStatus.NOT_FOUND);
      }

      // Check if user has access to this notification
      if (
        notification.organizationId !== req.user.organizationId ||
        notification.userId !== req.user.id
      ) {
        throw new HttpException(
          'You do not have permission to access this notification',
          HttpStatus.FORBIDDEN,
        );
      }

      return {
        success: true,
        data: notification,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch notification',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/read')
  async markAsRead(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    try {
      const notification = await this.notificationsService.findOne(id);

      if (!notification) {
        throw new HttpException('Notification not found', HttpStatus.NOT_FOUND);
      }

      // Check if user has access to this notification
      if (
        notification.organizationId !== req.user.organizationId ||
        notification.userId !== req.user.id
      ) {
        throw new HttpException(
          'You do not have permission to access this notification',
          HttpStatus.FORBIDDEN,
        );
      }

      const updatedNotification = await this.notificationsService.markAsRead(id);

      return {
        success: true,
        data: updatedNotification,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to mark notification as read',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('preferences')
  async getUserPreferences(@Req() req: AuthenticatedRequest) {
    try {
      const preferences = await this.notificationsService.getUserPreferences(
        req.user.id,
        req.user.organizationId,
      );

      return {
        success: true,
        data: preferences,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch notification preferences',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('preferences')
  async updateUserPreferences(
    @Body()
    preferencesDto: Array<{
      type: NotificationType;
      channel: NotificationChannel;
      enabled: boolean;
      settings?: Record<string, any>;
    }>,
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      const updatedPreferences = await this.notificationsService.updateUserPreferences(
        req.user.id,
        req.user.organizationId,
        preferencesDto,
      );

      return {
        success: true,
        data: updatedPreferences,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update notification preferences',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}