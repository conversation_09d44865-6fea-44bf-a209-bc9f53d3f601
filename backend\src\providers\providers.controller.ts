import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from "@nestjs/common";
import { ProvidersService } from "./providers.service";
import { CreateProviderDto } from "./dto/create-provider.dto";
import { UpdateProviderDto } from "./dto/update-provider.dto";
import { QueryProvidersDto } from "./dto/query-providers.dto";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";

interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
  timestamp: string;
  pagination?: any;
}

@Controller("api/v1/providers")
@UseGuards(JwtAuthGuard)
export class ProvidersController {
  constructor(private readonly providersService: ProvidersService) {}

  @Post()
  async create(
    @Body() createProviderDto: CreateProviderDto,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const provider = await this.providersService.create(
        createProviderDto,
        req.user.id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: provider,
        message: "Provider created successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get()
  async findAll(@Query() query: QueryProvidersDto, @Request() req) {
    try {
      const result = await this.providersService.findAll(
        query,
        req.user.organizationId,
      );
      return {
        success: true,
        data: result.data,
        pagination: result.pagination,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get("stats")
  async getStats(@Request() req): Promise<ApiResponse> {
    try {
      const stats = await this.providersService.getStats(
        req.user.organizationId,
      );
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get("available")
  async getAvailableProviders(): Promise<ApiResponse> {
    try {
      const providers = await this.providersService.getAvailableProviders();
      return {
        success: true,
        data: providers,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Get(":id")
  async findOne(
    @Param("id") id: string,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const provider = await this.providersService.findOne(
        id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: provider,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Patch(":id")
  async update(
    @Param("id") id: string,
    @Body() updateProviderDto: UpdateProviderDto,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const provider = await this.providersService.update(
        id,
        updateProviderDto,
        req.user.organizationId,
      );
      return {
        success: true,
        data: provider,
        message: "Provider updated successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Delete(":id")
  async remove(
    @Param("id") id: string,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      await this.providersService.remove(id, req.user.organizationId);
      return {
        success: true,
        message: "Provider deleted successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post(":id/test")
  async testProvider(
    @Param("id") id: string,
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const result = await this.providersService.testProvider(
        id,
        req.user.organizationId,
      );
      return {
        success: true,
        data: result,
        message: "Provider tested successfully",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  @Post("select")
  async selectProvider(
    @Body() body: { model: string; task: string; context?: any },
    @Request() req,
  ): Promise<ApiResponse> {
    try {
      const provider = await this.providersService.selectOptimalProvider(
        body.model,
        body.task,
        req.user.organizationId,
        body.context,
      );
      return {
        success: true,
        data: provider,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}