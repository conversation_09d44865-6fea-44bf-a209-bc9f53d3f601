import {
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>num,
  IsBoolean,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>,
  <PERSON>,
  IsArray,
} from "class-validator";
import { Transform } from "class-transformer";
import { AgentStatus } from "@prisma/client";

export class QueryAgentsDto {
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(AgentStatus)
  status?: AgentStatus;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true")
  isPublic?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  tags?: string[];

  @IsOptional()
  @IsString()
  sortBy?: string = "createdAt";

  @IsOptional()
  @IsEnum(["asc", "desc"])
  sortOrder?: "asc" | "desc" = "desc";
}
