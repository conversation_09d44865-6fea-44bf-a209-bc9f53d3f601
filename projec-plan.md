You are an expert fullstack AI developer. Start building SynapseAI — a universal, event-based, click-configurable AI orchestration system using NestJS, Next.js App Router, Redis, and PostgreSQL — without Docker or placeholder code.

✅ Rules

No mocks or placeholders — only real-world, fully functional, tested code.

Real PostgreSQL + Redis support

JWT + RBAC + Multi-tenant authentication

Typed WebSocket protocol (APIX)

Session-aware AI agent logic

Modular file structure that matches enterprise conventions

Typed SDK with real WebSocket event bindings

All modules must be production-ready with config, validation, retry/fallbacks, and schema definitions.

⚙️ Stack

Backend: NestJS (WebSocket, REST, PostgreSQL, Redis)

Frontend: Next.js 14 (App Router), Tailwind CSS, Shadcn UI

Client State: Zustand or Jotai

Security: JWT, HTTPS, RBAC

Infra: PM2 + NGINX + Certbot

🧩 Core Modules (Each Fully UI-Managable)

Auth System — JWT login/register, role/tenant access control

Agent Builder — Click-to-configure agents with state, memory, logic

Tool Manager — Stateless task APIs with input/output config

Tool Agent Builder — Hybrid agents combining tools and memory

Provider Manager — Configure AI providers and smart routing

Session Manager — Real-time Redis memory & TTL context sync

HITL Manager — Manage request_user_input, override decisions

Knowledge Base (RAG) — Upload files/URLs for searchable context

Widget Generator — Embed any agent/tool as JS, iframe, plugin

Analytics Dashboard — Track usage, latency, satisfaction metrics

User + Role Admin — Manage orgs, tenants, permissions

Live Preview Sandbox — Test agents/tools in real time

🔄 UAUI + APIX Protocol System Architecture

This section details the internal architecture of SynapseAI’s UAUI engine, its interaction with the WebSocket-based APIX layer, and how real-time flows operate across agents, tools, and providers.

🧠 UAUI Runtime Engine

UAUICore.ts handles agent reasoning, memory access, provider selection, and tool integration.

SmartProviderSelector dynamically routes to OpenAI, Claude, Gemini, Mistral, or Groq using real API calls and contextual evaluation.

EventBus manages broadcasted events with cross-app metadata, plugin middleware, and async subscriptions.

StateManager manages app-level state propagation and Redis-backed sync.

RouterEngine can emit cross-app commands such as inject_dom_instruction, route_to_dashboard, etc.

⚙️ @synapseai/aipx SDK Interface Layer

A production-grade, portable internal SDK that unifies UAUI logic into reusable modules for any frontend or backend.

export interface AIPXRequest {
  userId: string;
  sessionId: string;
  message: string;
  appType: 'widget' | 'dashboard' | 'crm';
  metadata?: Record<string, any>;
}

export interface AIPXResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
}


class AIPXCore {
  registerApp(appId: string, appType: string): AIPXApp;
  processAIRequest(request: AIPXRequest): Promise<AIPXResponse>;
  syncState(fromAppId: string, toAppId: string, state: Record<string, any>): void;
  onEvent(eventType: string, cb: (payload: any) => void): void;
}


🔌 APIX WebSocket Protocol

Supported production-grade events:

user_message

thinking_status

text_chunk

tool_call_start, tool_call_result, tool_call_error

request_user_input, user_response

state_update

error

control_signal

🔁 Data Flow

graph TD
U[User Input (UI)] --> WS[APIX WebSocket]
UI --> WS
WS --> UA[UAUICore Engine]

UA --> TM[ToolModule] --> EXT[External API]
UA --> PS[Provider Selector] --> LLM[LLM API]
UA --> SM[SessionManager (Redis)]

UA --> HITL[HITL Manager]
HITL --> HR[Human Responder]
HR --> HITL --> UA

UA --> EV[Emit Events]
EV --> UI[Client SDK]


🧪 Remaining Implementation Priorities

🔌 Multi-Provider Support (Production Logic)

Claude, Groq, Gemini, Mistral adapters using live APIs

Retry, fallback, timeout handling

Latency-aware scoring in SmartProviderSelector

UI toggle per provider per agent/tool

🔁 HITL (Human-in-the-Loop) System

request_user_input → HITL queue with UI dashboard

Admin override panel with response history log

Notifications for unresolved HITL prompts

Session state update after HITL response is submitted

HITL rule settings per agent/tool (optional/manual/auto)

🧠 UAUI Engine Completion

Memory injection pipeline from Redis to agent context

Prompt templating logic with variable binding

StateManager sync: multi-tab, multi-client real-time updates

Tool/agent schema validation via Zod at runtime

🔒 Security & Session Infrastructure

RBAC enforcement at route + UI-level (admin/dev/viewer)

Tenant isolation on DB + UI queries

Redis-backed session persistence

Rate limiting by tenant/user/module/tool

🧠 Tool-Agent Hybrid Concept

To maximize flexibility and capability, SynapseAI supports three interrelated module types:

Tool: Stateless API interface — best for tasks like sending emails, querying databases, or calling external services. Defined with input/output Zod schemas.

Agent: Memory-aware, reasoning-capable entity — powered by LLMs, ideal for conversation, planning, or orchestrating behavior.

Tool-Agent Hybrid: A powerful blend of both. Agents that:

Hold context/memory

Respond to user messages

Dynamically call tools during their reasoning

React to tool output and adapt behavior in real-time

The hybrid agent executes complex chains such as:

"Based on user profile, call pricingEstimatorTool, then summarize in user's language. If tool fails, fallback to manualPricingPolicy."

This pattern unlocks advanced automation, intelligent fallback, and logic-based workflows.

Tool-Agent hybrids are built in the Tool-Agent Builder module and executed in the ToolAgentRunner, combining:

Agent reasoning via LLM

Internal memory (Redis)

Real-time APIX events

Conditional tool invocation

Result post-processing


🧩 Tools & Agent Features

Tool schema config (input/output + validation)

Test harness for each tool (manual + mock request runner)

Agent workflow editor step builder + reorder support

Tool-Agent runner with context and response merging

🔍 RAG + Knowledge Base

Source uploader (file, URL, plain text, DB query)

Document parser + auto-tagger

FAISS vectorization and search config

Source testing UI: query preview, document matching, fallback modes

📦 SDK, CLI, & Protocol

@synapseai/sdk typed export for APIX events and UAUI types

CLI: init, run, build, preview, sync

WebSocket client auto-connect, keepalive, retry

SDK auth token handling, error handling, and hook lifecycle

🧪 Widget & Embed Infrastructure

Widget code generator for JS/iFrame/CMS plugin

Live Preview with config binding (theme, placement, greeting)

Responsive mode toggles (desktop/tablet/mobile)

Branding settings with logo, color, typography injection

📊 Analytics & Telemetry

Per-tool/agent usage tracking (invokes, time, token cost)

Engagement tracking: bounce, chat depth, drop-off points

Dashboard UI: graphs, filters, time range, export CSV

Predictive insights: most used tools, inactive agents, avg latency

🌐 Production DevOps

PM2 process manager for backend

Certbot TLS for NGINX

.env loader and environment safety checks

CI/CD pipeline hooks

# � PRODUCTION IMPLEMENTATION PLAN

## Core Module Implementation Order

### Phase 1: Foundation (Weeks 1-2)
**UAUI Engine + APIX Protocol**

### Phase 2: Primary Value (Weeks 3-4)
**Agent Builder**

### Phase 3: AI Orchestration (Weeks 5-6)
**Provider Manager**

### Phase 4: Extensibility (Weeks 7-8)
**Tool Manager**

### Phase 5: State Persistence (Weeks 9-10)
**Session Manager**

### Phase 6: Advanced Workflows (Weeks 11-12)
**Tool-Agent Hybrids**

### Phase 7: Distribution (Weeks 13-14)
**Widget Generator**

---

## 🔨 DETAILED TASK BREAKDOWN

### PHASE 1: UAUI ENGINE + APIX FOUNDATION (Weeks 1-2)

#### Backend Infrastructure
- [ ] Bootstrap NestJS monorepo structure
- [ ] Setup PostgreSQL with Prisma ORM
- [ ] Setup Redis connection
- [ ] Implement basic JWT authentication
- [ ] Create WebSocket gateway (/apix)
- [ ] Define APIX event types (TypeScript)
- [ ] Implement UAUICore engine skeleton
- [ ] Create basic provider interface
- [ ] Setup OpenAI provider adapter (real API)
- [ ] Implement event emission system

#### Frontend Infrastructure
- [ ] Bootstrap Next.js 14 App Router
- [ ] Setup Tailwind CSS + Shadcn UI
- [ ] Create WebSocket client connection
- [ ] Implement Zustand state management
- [ ] Create basic authentication pages
- [ ] Setup APIX event listeners
- [ ] Create basic chat interface for testing

#### Database Schema
- [ ] Users table with JWT support
- [ ] Organizations table (multi-tenant)
- [ ] Agents table with configuration
- [ ] Providers table with API keys
- [ ] Sessions table for Redis sync

### PHASE 2: AGENT BUILDER (Weeks 3-4)

#### Backend Agent System
- [ ] Agent CRUD operations (real DB)
- [ ] Agent execution engine
- [ ] Prompt template system
- [ ] Agent memory injection
- [ ] Agent-to-provider routing
- [ ] Real-time agent status events
- [ ] Agent configuration validation

#### Frontend Agent Builder
- [ ] Agent creation form
- [ ] Prompt template editor
- [ ] Model selection dropdown
- [ ] Temperature/token controls
- [ ] Agent testing interface
- [ ] Agent list/management view
- [ ] Live agent preview chat

### PHASE 3: PROVIDER MANAGER (Weeks 5-6)

#### Backend Provider System
- [ ] Provider CRUD operations
- [ ] OpenAI adapter (production)
- [ ] Claude adapter (production)
- [ ] Gemini adapter (production)
- [ ] Smart provider selection logic
- [ ] Provider health monitoring
- [ ] Usage tracking per provider
- [ ] Provider failover handling

#### Frontend Provider Manager
- [ ] Provider configuration forms
- [ ] API key management interface
- [ ] Provider testing tools
- [ ] Usage analytics dashboard
- [ ] Provider enable/disable toggles
- [ ] Provider performance metrics

### PHASE 4: TOOL MANAGER (Weeks 7-8)

#### Backend Tool System
- [ ] Tool CRUD operations
- [ ] Tool schema validation (Zod)
- [ ] Tool execution engine
- [ ] External API integration
- [ ] Tool result processing
- [ ] Tool error handling
- [ ] Tool usage tracking

#### Frontend Tool Manager
- [ ] Tool creation interface
- [ ] Schema editor (JSON/form)
- [ ] Tool testing harness
- [ ] Tool library/marketplace view
- [ ] Tool configuration forms
- [ ] Tool execution logs

### PHASE 5: SESSION MANAGER (Weeks 9-10)

#### Backend Session System
- [ ] Redis session storage
- [ ] Session TTL management
- [ ] Cross-session state sync
- [ ] Session-based memory
- [ ] Multi-tab synchronization
- [ ] Session cleanup jobs
- [ ] Session analytics

#### Frontend Session Features
- [ ] Session viewer interface
- [ ] Session history browser
- [ ] Multi-tab state sync
- [ ] Session debugging tools
- [ ] Session export/import
- [ ] Session analytics view

### PHASE 6: TOOL-AGENT HYBRIDS (Weeks 11-12)

#### Backend Hybrid System
- [ ] Hybrid agent execution engine
- [ ] Tool invocation from agents
- [ ] Conditional tool execution
- [ ] Tool result integration
- [ ] Hybrid workflow orchestration
- [ ] Complex chain execution
- [ ] Hybrid error handling

#### Frontend Hybrid Builder
- [ ] Hybrid agent creation interface
- [ ] Tool selection for agents
- [ ] Workflow visual editor
- [ ] Conditional logic builder
- [ ] Hybrid testing interface
- [ ] Workflow debugging tools

### PHASE 7: WIDGET GENERATOR (Weeks 13-14)

#### Backend Widget System
- [ ] Widget configuration API
- [ ] Widget authentication
- [ ] Embeddable agent endpoints
- [ ] Widget analytics tracking
- [ ] Widget customization options
- [ ] Widget security controls

#### Frontend Widget Generator
- [ ] Widget configuration interface
- [ ] Live widget preview
- [ ] Embed code generator
- [ ] Widget customization tools
- [ ] Widget analytics dashboard
- [ ] Widget distribution tools

✅ Final Output Expectation

100% functional app — no partial/mocked code

Ready for deployment and integration

AI-readable modular architecture

Schema-complete APIX + UAUI protocol

Embeddable SDK + UI




### 🧠 SynapseAI System: Visual Explanation of Production Architecture

This document summarizes the **visual layout and flow** of the entire SynapseAI architecture as described in the production prompt.

---

### 📦 High-Level System Diagram

```mermaid
graph TD

subgraph Frontend [Frontend UI (Next.js)]
  LoginPage[Auth Pages]
  AgentUI[Agent Builder UI]
  ToolUI[Tool Configurator]
  ToolAgentUI[Tool-Agent Hybrid Builder]
  ProviderUI[Provider Manager]
  SessionUI[Session Viewer]
  HITLUI[HITL Dashboard]
  KBUI[Knowledge Base Manager]
  WidgetUI[Widget Generator]
  PreviewUI[Live Chat Preview Sandbox]
end

subgraph SDK [Client SDK / Zustand State]
  ClientWS[WebSocket Client]
  ClientStore[Session Store]
end

subgraph Backend [NestJS Backend]
  Auth[Auth Module]
  AgentModule[Agent Module]
  ToolModule[Tool Module]
  ToolAgentRunner[Tool-Agent Runner]
  Provider[Provider Manager]
  Session[Session Manager]
  HITL[HITL Manager]
  KB[RAG Module]
  Gateway[WebSocket Gateway]
  UAUI[UAUICore Engine]
end

subgraph External [External Services]
  Redis[Redis]
  PG[PostgreSQL]
  OpenAI[OpenAI API]
  Claude[Claude API]
  Gemini[Gemini API]
  ExternalAPI[External Tools / APIs]
end

LoginPage --> Auth
AgentUI --> AgentModule
ToolUI --> ToolModule
ToolAgentUI --> ToolAgentRunner
ProviderUI --> Provider
SessionUI --> Session
HITLUI --> HITL
KBUI --> KB
WidgetUI --> AgentModule
PreviewUI --> Gateway

ClientWS --> Gateway
ClientStore --> Session

Gateway --> UAUI
UAUI --> Session
UAUI --> Provider
UAUI --> ToolModule
UAUI --> ToolAgentRunner
UAUI --> HITL
UAUI --> KB

Provider --> OpenAI & Claude & Gemini
ToolModule --> ExternalAPI
Session --> Redis
All modules --> PG
```

---

### 🔁 Tool-Agent Execution Flow

```mermaid
graph TD
UserMessage[User Message] --> WebSocket
WebSocket --> UAUICore
UAUICore --> AgentLogic
AgentLogic --> ToolCall[Call Tool]
ToolCall --> ToolAPI[Tool API Execution]
ToolAPI --> ToolResponse
ToolResponse --> UAUICore
UAUICore --> FinalLLM
FinalLLM --> EmitChunks
EmitChunks --> UIResponse
```

---

### 🎛️ UI-Controlled Modules View

Each of the following is a **tabbed, form-driven, or click-based** UI panel:

* Agent Builder: Visual workflow editor for reasoning steps
* Tool Configurator: Form-based API input/output with schema validator
* Tool-Agent Builder: Combines both above with conditional flows
* Provider Manager: Add/edit API keys, enable/disable per agent
* HITL Manager: See incoming user\_input prompts and respond manually
* Session Viewer: Redis-backed context debugging panel
* Knowledge Base: Upload files/URLs and configure embeddings
* Widget Generator: Live preview with theme/greeting/button config
* Analytics Dashboard: Visual stats with filters and exports

---
