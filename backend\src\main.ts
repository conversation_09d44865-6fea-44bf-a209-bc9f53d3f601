import { NestFactory } from "@nestjs/core";
import { ValidationPipe } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as Sentry from "@sentry/node";
import { ProfilingIntegration } from "@sentry/profiling-node";
import helmet from "helmet";
import { AppModule } from "./app.module";
import { WinstonLogger } from "./common/logger/winston.logger";
import { AllExceptionsFilter } from "./common/filters/all-exceptions.filter";

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: new WinstonLogger(),
  });

  const configService = app.get(ConfigService);

  // Initialize Sentry
  Sentry.init({
    dsn: configService.get("SENTRY_DSN"),
    integrations: [new ProfilingIntegration()],
    tracesSampleRate: 1.0,
    profilesSampleRate: 1.0,
  });

  // Security middleware
  app.use(helmet());

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Global exception filter
  app.useGlobalFilters(new AllExceptionsFilter());

  // CORS configuration
  app.enableCors({
    origin: configService.get("FRONTEND_URL", "http://localhost:3000"),
    credentials: true,
  });

  const port = configService.get("PORT", 3001);
  await app.listen(port);

  console.log(`🚀 SynapseAI Backend running on port ${port}`);
}

bootstrap();
