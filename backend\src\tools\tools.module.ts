import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { ToolsService } from "./tools.service";
import { ToolsController } from "./tools.controller";
import { PrismaModule } from "../prisma/prisma.module";
import { BillingModule } from "../billing/billing.module";
import { AnalyticsModule } from "../analytics/analytics.module";
import { NotificationsModule } from "../notifications/notifications.module";
import { ApixModule } from "../apix/apix.module";

@Module({
  imports: [
    PrismaModule,
    BillingModule,
    AnalyticsModule,
    NotificationsModule,
    ApixModule,
  ],
  controllers: [ToolsController],
  providers: [ToolsService],
  exports: [ToolsService],
})
export class ToolsModule {}
