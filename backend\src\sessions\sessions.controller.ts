import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { SessionsService } from './sessions.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthenticatedRequest } from '../common/middleware/tenant.middleware';
import { MessageRole, SessionStatus } from '@prisma/client';

@Controller('api/v1/sessions')
@UseGuards(JwtAuthGuard)
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @Post()
  async create(
    @Body()
    createSessionDto: {
      title?: string;
      context?: Record<string, any>;
      agentId?: string;
      hybridId?: string;
      workflowId?: string;
    },
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      const session = await this.sessionsService.create({
        ...createSessionDto,
        userId: req.user.id,
        organizationId: req.user.organizationId,
      });

      return {
        success: true,
        data: session,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create session',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async findAll(
    @Query('userId') userId: string,
    @Query('status') status: SessionStatus,
    @Query('agentId') agentId: string,
    @Query('hybridId') hybridId: string,
    @Query('workflowId') workflowId: string,
    @Query('limit') limit: number,
    @Query('offset') offset: number,
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      const result = await this.sessionsService.findAll(
        req.user.organizationId,
        {
          userId,
          status,
          agentId,
          hybridId,
          workflowId,
          limit: limit ? Number(limit) : undefined,
          offset: offset ? Number(offset) : undefined,
        },
      );

      return {
        success: true,
        data: result.data,
        meta: result.meta,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch sessions',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    try {
      const session = await this.sessionsService.findOne(id);

      // Check if user has access to this session
      if (
        session.organizationId !== req.user.organizationId &&
        session.userId !== req.user.id &&
        req.user.role !== 'SUPER_ADMIN'
      ) {
        throw new HttpException(
          'You do not have permission to access this session',
          HttpStatus.FORBIDDEN,
        );
      }

      return {
        success: true,
        data: session,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch session',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body()
    updateSessionDto: {
      title?: string;
      context?: Record<string, any>;
      memory?: Record<string, any>;
      status?: SessionStatus;
      metadata?: Record<string, any>;
    },
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      // Check if user has access to this session
      const session = await this.sessionsService.findOne(id);
      if (
        session.organizationId !== req.user.organizationId &&
        session.userId !== req.user.id &&
        req.user.role !== 'SUPER_ADMIN'
      ) {
        throw new HttpException(
          'You do not have permission to update this session',
          HttpStatus.FORBIDDEN,
        );
      }

      const updatedSession = await this.sessionsService.update(
        id,
        updateSessionDto,
      );

      return {
        success: true,
        data: updatedSession,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to update session',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/messages')
  async addMessage(
    @Param('id') id: string,
    @Body()
    messageDto: {
      content: string;
      role: MessageRole;
      metadata?: Record<string, any>;
    },
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      // Check if user has access to this session
      const session = await this.sessionsService.findOne(id);
      if (
        session.organizationId !== req.user.organizationId &&
        session.userId !== req.user.id &&
        req.user.role !== 'SUPER_ADMIN'
      ) {
        throw new HttpException(
          'You do not have permission to add messages to this session',
          HttpStatus.FORBIDDEN,
        );
      }

      const message = await this.sessionsService.addMessage({
        sessionId: id,
        ...messageDto,
      });

      return {
        success: true,
        data: message,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to add message',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id/messages')
  async getMessages(
    @Param('id') id: string,
    @Query('limit') limit: number,
    @Query('offset') offset: number,
    @Query('role') role: MessageRole,
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      // Check if user has access to this session
      const session = await this.sessionsService.findOne(id);
      if (
        session.organizationId !== req.user.organizationId &&
        session.userId !== req.user.id &&
        req.user.role !== 'SUPER_ADMIN'
      ) {
        throw new HttpException(
          'You do not have permission to view messages in this session',
          HttpStatus.FORBIDDEN,
        );
      }

      const result = await this.sessionsService.getMessages(id, {
        limit: limit ? Number(limit) : undefined,
        offset: offset ? Number(offset) : undefined,
        role,
      });

      return {
        success: true,
        data: result.data,
        meta: result.meta,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch messages',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':id/complete')
  async completeSession(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    try {
      // Check if user has access to this session
      const session = await this.sessionsService.findOne(id);
      if (
        session.organizationId !== req.user.organizationId &&
        session.userId !== req.user.id &&
        req.user.role !== 'SUPER_ADMIN'
      ) {
        throw new HttpException(
          'You do not have permission to complete this session',
          HttpStatus.FORBIDDEN,
        );
      }

      const completedSession = await this.sessionsService.completeSession(id);

      return {
        success: true,
        data: completedSession,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to complete session',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  async delete(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    try {
      // Check if user has access to this session
      const session = await this.sessionsService.findOne(id);
      if (
        session.organizationId !== req.user.organizationId &&
        session.userId !== req.user.id &&
        req.user.role !== 'SUPER_ADMIN'
      ) {
        throw new HttpException(
          'You do not have permission to delete this session',
          HttpStatus.FORBIDDEN,
        );
      }

      await this.sessionsService.delete(id);

      return {
        success: true,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to delete session',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}