"use client";

import React, { useState } from "react";
import { apiClient } from "@/lib/api";
import { useApi } from "@/hooks/useApi";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Activity,
  Clock,
  DollarSign,
  Eye,
  MoreHorizontal,
  RefreshCw,
  Zap,
  CheckCircle,
  XCircle,
  Loader,
} from "lucide-react";

interface AgentExecution {
  id: string;
  status: string;
  input: any;
  output?: any;
  error?: string;
  duration?: number;
  tokens?: number;
  cost?: number;
  createdAt: string;
  completedAt?: string;
}

interface AgentExecutionsProps {
  agentId: string;
}

const AgentExecutions = ({ agentId }: AgentExecutionsProps) => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [selectedExecution, setSelectedExecution] =
    useState<AgentExecution | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  const {
    data: executionsResponse,
    loading,
    error,
    refetch,
  } = useApi(() => apiClient.getAgentExecutions(agentId, page, limit), {
    immediate: true,
  });

  const executions = executionsResponse?.data || [];
  const pagination = executionsResponse?.pagination;

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleViewDetails = (execution: AgentExecution) => {
    setSelectedExecution(execution);
    setDetailsOpen(true);
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "failed":
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "running":
      case "pending":
        return <Loader className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
      case "success":
        return "bg-green-100 text-green-800 border-green-200";
      case "failed":
      case "error":
        return "bg-red-100 text-red-800 border-red-200";
      case "running":
      case "pending":
        return "bg-blue-100 text-blue-800 border-blue-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return "N/A";
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const formatCost = (cost?: number) => {
    if (!cost) return "N/A";
    return `$${cost.toFixed(4)}`;
  };

  const formatTokens = (tokens?: number) => {
    if (!tokens) return "N/A";
    return tokens.toLocaleString();
  };

  if (error) {
    return (
      <Card className="bg-white">
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading executions: {error}</p>
            <Button onClick={refetch} className="mt-2">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card className="bg-white">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-500" />
              Execution History
            </CardTitle>
            <Button onClick={refetch} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Total</span>
              </div>
              <p className="text-lg font-bold mt-1">{pagination?.total || 0}</p>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Successful</span>
              </div>
              <p className="text-lg font-bold mt-1">
                {
                  executions.filter(
                    (e) =>
                      e.status.toLowerCase() === "completed" ||
                      e.status.toLowerCase() === "success",
                  ).length
                }
              </p>
            </div>
            <div className="bg-red-50 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium">Failed</span>
              </div>
              <p className="text-lg font-bold mt-1">
                {
                  executions.filter(
                    (e) =>
                      e.status.toLowerCase() === "failed" ||
                      e.status.toLowerCase() === "error",
                  ).length
                }
              </p>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center gap-2">
                <Loader className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Running</span>
              </div>
              <p className="text-lg font-bold mt-1">
                {
                  executions.filter(
                    (e) =>
                      e.status.toLowerCase() === "running" ||
                      e.status.toLowerCase() === "pending",
                  ).length
                }
              </p>
            </div>
          </div>

          {/* Executions Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Status</TableHead>
                  <TableHead>Started</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Tokens</TableHead>
                  <TableHead>Cost</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Skeleton className="h-4 w-[80px]" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-[120px]" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-[80px]" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-[60px]" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-[60px]" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-[30px]" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : executions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <Activity className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No executions found</p>
                        <p className="text-sm text-gray-400">
                          Execute this agent to see execution history here.
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  executions.map((execution) => (
                    <TableRow key={execution.id} className="hover:bg-gray-50">
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(execution.status)}
                          <Badge className={getStatusColor(execution.status)}>
                            {execution.status}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell className="text-sm text-gray-600">
                        {new Date(execution.createdAt).toLocaleString()}
                      </TableCell>
                      <TableCell className="text-sm">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3 text-gray-400" />
                          {formatDuration(execution.duration)}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm">
                        <div className="flex items-center gap-1">
                          <Zap className="h-3 w-3 text-gray-400" />
                          {formatTokens(execution.tokens)}
                        </div>
                      </TableCell>
                      <TableCell className="text-sm">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3 text-gray-400" />
                          {formatCost(execution.cost)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => handleViewDetails(execution)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex justify-center mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() =>
                        handlePageChange(Math.max(1, pagination.page - 1))
                      }
                      className={
                        pagination.page === 1
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                  {Array.from(
                    { length: Math.min(5, pagination.totalPages) },
                    (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            onClick={() => handlePageChange(pageNum)}
                            isActive={pageNum === pagination.page}
                            className="cursor-pointer"
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    },
                  )}
                  <PaginationItem>
                    <PaginationNext
                      onClick={() =>
                        handlePageChange(
                          Math.min(pagination.totalPages, pagination.page + 1),
                        )
                      }
                      className={
                        pagination.page === pagination.totalPages
                          ? "pointer-events-none opacity-50"
                          : "cursor-pointer"
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Execution Details Dialog */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-500" />
              Execution Details
            </DialogTitle>
          </DialogHeader>
          {selectedExecution && (
            <ScrollArea className="max-h-[70vh] pr-4">
              <div className="space-y-6">
                {/* Status and Metadata */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Status
                      </label>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusIcon(selectedExecution.status)}
                        <Badge
                          className={getStatusColor(selectedExecution.status)}
                        >
                          {selectedExecution.status}
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Execution ID
                      </label>
                      <p className="text-sm font-mono mt-1">
                        {selectedExecution.id}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Started At
                      </label>
                      <p className="text-sm mt-1">
                        {new Date(selectedExecution.createdAt).toLocaleString()}
                      </p>
                    </div>
                    {selectedExecution.completedAt && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">
                          Completed At
                        </label>
                        <p className="text-sm mt-1">
                          {new Date(
                            selectedExecution.completedAt,
                          ).toLocaleString()}
                        </p>
                      </div>
                    )}
                  </div>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Duration
                      </label>
                      <p className="text-sm mt-1">
                        {formatDuration(selectedExecution.duration)}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Tokens Used
                      </label>
                      <p className="text-sm mt-1">
                        {formatTokens(selectedExecution.tokens)}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">
                        Cost
                      </label>
                      <p className="text-sm mt-1">
                        {formatCost(selectedExecution.cost)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Input */}
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    Input
                  </label>
                  <div className="mt-2 p-3 bg-gray-50 border rounded-md">
                    <pre className="text-sm whitespace-pre-wrap overflow-auto max-h-32">
                      {typeof selectedExecution.input === "string"
                        ? selectedExecution.input
                        : JSON.stringify(selectedExecution.input, null, 2)}
                    </pre>
                  </div>
                </div>

                {/* Output */}
                {selectedExecution.output && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      Output
                    </label>
                    <div className="mt-2 p-3 bg-gray-50 border rounded-md">
                      <pre className="text-sm whitespace-pre-wrap overflow-auto max-h-32">
                        {typeof selectedExecution.output === "string"
                          ? selectedExecution.output
                          : JSON.stringify(selectedExecution.output, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                {/* Error */}
                {selectedExecution.error && (
                  <div>
                    <label className="text-sm font-medium text-red-600">
                      Error
                    </label>
                    <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                      <pre className="text-sm text-red-800 whitespace-pre-wrap overflow-auto max-h-32">
                        {selectedExecution.error}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AgentExecutions;
