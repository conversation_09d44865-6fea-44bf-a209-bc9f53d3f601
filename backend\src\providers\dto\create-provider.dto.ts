import { IsString, <PERSON><PERSON><PERSON>, IsObject, Is<PERSON><PERSON>y, IsOptional, IsBoolean, IsN<PERSON>ber } from 'class-validator';
import { ProviderType, ProviderStatus } from '@prisma/client';

export class CreateProviderDto {
  @IsString()
  name!: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(ProviderType)
  type!: ProviderType;

  @IsObject()
  configuration!: Record<string, any>;

  @IsArray()
  @IsString({ each: true })
  models!: string[];

  @IsArray()
  @IsString({ each: true })
  capabilities!: string[];

  @IsOptional()
  @IsEnum(ProviderStatus)
  status?: ProviderStatus;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsNumber()
  reliability?: number;

  @IsOptional()
  @IsNumber()
  costPerToken?: number;

  @IsOptional()
  @IsNumber()
  averageLatency?: number;

  @IsOptional()
  @IsNumber()
  priority?: number;

  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}