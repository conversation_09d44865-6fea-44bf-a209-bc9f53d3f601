"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { apiClient } from "@/lib/api";
import { Provider, ProviderType, ProviderStatus } from "../../../shared/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertCircle, Eye, EyeOff, X } from "lucide-react";

interface ProviderFormProps {
  provider?: Provider;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  description: string;
  type: ProviderType;
  apiKey: string;
  baseUrl?: string;
  models: string[];
  isActive: boolean;
  priority: number;
}

const PROVIDER_CONFIGS = {
  [ProviderType.OPENAI]: {
    name: "OpenAI",
    defaultModels: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
    capabilities: ["text", "chat", "embeddings"],
    fields: ["apiKey"],
  },
  [ProviderType.ANTHROPIC]: {
    name: "Anthropic Claude",
    defaultModels: [
      "claude-3-opus-20240229",
      "claude-3-sonnet-20240229",
      "claude-3-haiku-20240307",
    ],
    capabilities: ["text", "chat"],
    fields: ["apiKey"],
  },
  [ProviderType.GOOGLE]: {
    name: "Google Gemini",
    defaultModels: ["gemini-pro", "gemini-pro-vision"],
    capabilities: ["text", "chat", "vision"],
    fields: ["apiKey"],
  },
  [ProviderType.MISTRAL]: {
    name: "Mistral AI",
    defaultModels: ["mistral-large", "mistral-medium", "mistral-small"],
    capabilities: ["text", "chat"],
    fields: ["apiKey"],
  },
  [ProviderType.GROQ]: {
    name: "Groq",
    defaultModels: ["llama2-70b-4096", "mixtral-8x7b-32768"],
    capabilities: ["text", "chat"],
    fields: ["apiKey"],
  },
  [ProviderType.CUSTOM]: {
    name: "Custom Provider",
    defaultModels: [],
    capabilities: ["text", "chat"],
    fields: ["apiKey", "baseUrl"],
  },
};

export default function ProviderForm({
  provider,
  onSuccess,
}: ProviderFormProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [customModel, setCustomModel] = useState("");
  const [availableProviders, setAvailableProviders] = useState<any[]>([]);

  const form = useForm<FormData>({
    defaultValues: {
      name: provider?.name || "",
      description: provider?.description || "",
      type: provider?.type || ProviderType.OPENAI,
      apiKey: "",
      baseUrl: provider?.configuration?.baseUrl || "",
      models: provider?.models || [],
      isActive: provider?.isActive ?? true,
      priority: provider?.priority || 1,
    },
  });

  const watchedType = form.watch("type");
  const config = PROVIDER_CONFIGS[watchedType];

  useEffect(() => {
    const fetchAvailableProviders = async () => {
      try {
        const response = await apiClient.getAvailableProviders();
        if (response.success) {
          setAvailableProviders(response.data || []);
        }
      } catch (err) {
        console.error("Failed to fetch available providers:", err);
      }
    };

    fetchAvailableProviders();
  }, []);

  useEffect(() => {
    if (provider) {
      setSelectedModels(provider.models);
    } else {
      setSelectedModels(config.defaultModels);
    }
  }, [watchedType, provider]);

  const handleAddCustomModel = () => {
    if (customModel.trim() && !selectedModels.includes(customModel.trim())) {
      setSelectedModels([...selectedModels, customModel.trim()]);
      setCustomModel("");
    }
  };

  const handleRemoveModel = (model: string) => {
    setSelectedModels(selectedModels.filter((m) => m !== model));
  };

  const onSubmit = async (data: FormData) => {
    try {
      setLoading(true);
      setError(null);

      const providerData = {
        name: data.name,
        description: data.description,
        type: data.type,
        configuration: {
          apiKey: data.apiKey,
          ...(data.baseUrl && { baseUrl: data.baseUrl }),
        },
        models: selectedModels,
        capabilities: config.capabilities,
        status: ProviderStatus.ACTIVE,
        isActive: data.isActive,
        reliability: provider?.reliability || 1.0,
        costPerToken: provider?.costPerToken || 0.001,
        averageLatency: provider?.averageLatency || 1000,
        priority: data.priority,
        metadata: {},
      };

      let response;
      if (provider) {
        response = await apiClient.updateProvider(provider.id, providerData);
      } else {
        response = await apiClient.createProvider(providerData);
      }

      if (response.success) {
        onSuccess();
      } else {
        setError(response.error || "Failed to save provider");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save provider");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            rules={{ required: "Name is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Provider Name</FormLabel>
                <FormControl>
                  <Input placeholder="My OpenAI Provider" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            rules={{ required: "Type is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Provider Type</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select provider type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(PROVIDER_CONFIGS).map(([type, config]) => (
                      <SelectItem key={type} value={type}>
                        {config.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Input placeholder="Optional description" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="apiKey"
              rules={{ required: "API Key is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>API Key</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type={showApiKey ? "text" : "password"}
                        placeholder="Enter your API key"
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Your API key will be encrypted and stored securely
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {config.fields.includes("baseUrl") && (
              <FormField
                control={form.control}
                name="baseUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Base URL</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://api.example.com/v1"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Custom API endpoint URL (optional)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Models</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Available Models</div>
              <div className="flex flex-wrap gap-2">
                {config.defaultModels.map((model) => (
                  <div key={model} className="flex items-center space-x-2">
                    <Checkbox
                      id={model}
                      checked={selectedModels.includes(model)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedModels([...selectedModels, model]);
                        } else {
                          setSelectedModels(
                            selectedModels.filter((m) => m !== model),
                          );
                        }
                      }}
                    />
                    <label htmlFor={model} className="text-sm">
                      {model}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <div className="text-sm font-medium">Add Custom Model</div>
              <div className="flex gap-2">
                <Input
                  placeholder="model-name"
                  value={customModel}
                  onChange={(e) => setCustomModel(e.target.value)}
                  onKeyPress={(e) =>
                    e.key === "Enter" &&
                    (e.preventDefault(), handleAddCustomModel())
                  }
                />
                <Button type="button" onClick={handleAddCustomModel}>
                  Add
                </Button>
              </div>
            </div>

            {selectedModels.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm font-medium">Selected Models</div>
                <div className="flex flex-wrap gap-2">
                  {selectedModels.map((model) => (
                    <Badge
                      key={model}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {model}
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => handleRemoveModel(model)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Enable this provider for use
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="10"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value) || 1)
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      Higher priority providers are selected first (1-10)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-2">
          <Button
            type="submit"
            disabled={loading || selectedModels.length === 0}
          >
            {loading
              ? "Saving..."
              : provider
                ? "Update Provider"
                : "Create Provider"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
