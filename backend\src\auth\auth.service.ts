import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  Logger,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { PrismaService } from "../prisma/prisma.service";
import * as bcrypt from "bcrypt";
import { User, UserRole } from "@prisma/client";

export interface JwtPayload {
  sub: string;
  email: string;
  organizationId: string;
  role: UserRole;
}

export interface LoginDto {
  email: string;
  password: string;
}

export interface RegisterDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  organizationName?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<User | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email },
        include: { organization: true },
      });

      if (!user || !user.passwordHash) {
        return null;
      }

      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
      if (!isPasswordValid) {
        return null;
      }

      if (!user.isActive) {
        throw new UnauthorizedException("Account is inactive");
      }

      if (user.organization.status !== "ACTIVE") {
        throw new UnauthorizedException("Organization is not active");
      }

      return user;
    } catch (error) {
      this.logger.error("User validation failed:", error);
      return null;
    }
  }

  async login(loginDto: LoginDto) {
    const user = await this.validateUser(loginDto.email, loginDto.password);
    if (!user) {
      throw new UnauthorizedException("Invalid credentials");
    }

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      organizationId: user.organizationId,
      role: user.role,
    };

    const accessToken = this.jwtService.sign(payload);

    // Update last login timestamp
    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    this.logger.log(`User ${user.email} logged in successfully`);

    return {
      accessToken,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        organizationId: user.organizationId,
      },
    };
  }

  async register(registerDto: RegisterDto) {
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: registerDto.email },
    });

    if (existingUser) {
      throw new ConflictException("User with this email already exists");
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(registerDto.password, saltRounds);

    try {
      const result = await this.prisma.$transaction(async (prisma) => {
        // Create organization if provided
        let organization;
        if (registerDto.organizationName) {
          const slug = this.generateSlug(registerDto.organizationName);
          organization = await prisma.organization.create({
            data: {
              name: registerDto.organizationName,
              slug,
              status: "ACTIVE",
            },
          });
        } else {
          // Find a default organization or create one
          organization = await prisma.organization.findFirst({
            where: { slug: "default" },
          });

          if (!organization) {
            organization = await prisma.organization.create({
              data: {
                name: "Default Organization",
                slug: "default",
                status: "ACTIVE",
              },
            });
          }
        }

        // Create user
        const user = await prisma.user.create({
          data: {
            email: registerDto.email,
            passwordHash,
            firstName: registerDto.firstName,
            lastName: registerDto.lastName,
            role: registerDto.organizationName ? "ORG_ADMIN" : "DEVELOPER",
            organizationId: organization.id,
          },
          include: {
            organization: true,
          },
        });

        return user;
      });

      this.logger.log(`User ${registerDto.email} registered successfully`);

      // Generate JWT token
      const payload: JwtPayload = {
        sub: result.id,
        email: result.email,
        organizationId: result.organizationId,
        role: result.role,
      };

      const accessToken = this.jwtService.sign(payload);

      return {
        accessToken,
        user: {
          id: result.id,
          email: result.email,
          firstName: result.firstName,
          lastName: result.lastName,
          role: result.role,
          organizationId: result.organizationId,
        },
      };
    } catch (error) {
      this.logger.error("User registration failed:", error);
      throw new ConflictException("Failed to create user account");
    }
  }

  async refreshToken(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { organization: true },
    });

    if (!user || !user.isActive || user.organization.status !== "ACTIVE") {
      throw new UnauthorizedException("Invalid user or inactive account");
    }

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      organizationId: user.organizationId,
      role: user.role,
    };

    return {
      accessToken: this.jwtService.sign(payload),
    };
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "")
      .substring(0, 50);
  }

  async validateJwtPayload(payload: JwtPayload): Promise<User | null> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: { organization: true },
      });

      if (
        !user ||
        !user.isActive ||
        user.organization.status !== "ACTIVE"
      ) {
        return null;
      }

      return user;
    } catch (error) {
      this.logger.error("JWT payload validation failed:", error);
      return null;
    }
  }
}
