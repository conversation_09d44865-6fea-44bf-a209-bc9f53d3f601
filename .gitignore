# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Tempo

node_modules/
**/tempobook/dynamic/
**/tempobook/storyboards/

# Backend specific
backend/node_modules/
backend/dist/
backend/logs/
backend/prisma/migrations/

# Docker
.docker/
docker-compose.override.yml

# Monitoring
monitoring/data/

# Shared types build
shared/dist/

# remove git history
.git

.github/
