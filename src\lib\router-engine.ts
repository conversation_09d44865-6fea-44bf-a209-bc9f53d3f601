"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";

export interface RouterCommand {
  type: "navigate" | "replace" | "back" | "forward" | "refresh" | "external";
  path?: string;
  params?: Record<string, string>;
  query?: Record<string, string>;
  hash?: string;
  external?: boolean;
  newTab?: boolean;
}

export interface RouterEngineOptions {
  baseUrl?: string;
  enableHistory?: boolean;
  enableAnalytics?: boolean;
}

export class RouterEngine {
  private router: any;
  private options: RouterEngineOptions;
  private history: RouterCommand[] = [];

  constructor(router: any, options: RouterEngineOptions = {}) {
    this.router = router;
    this.options = {
      baseUrl: "",
      enableHistory: true,
      enableAnalytics: false,
      ...options,
    };
  }

  private buildUrl(command: RouterCommand): string {
    if (command.type === "external" && command.path) {
      return command.path;
    }

    let url = command.path || "/";
    
    // Replace path parameters
    if (command.params) {
      Object.entries(command.params).forEach(([key, value]) => {
        url = url.replace(`:${key}`, encodeURIComponent(value));
      });
    }

    // Add query parameters
    if (command.query) {
      const searchParams = new URLSearchParams();
      Object.entries(command.query).forEach(([key, value]) => {
        searchParams.set(key, value);
      });
      const queryString = searchParams.toString();
      if (queryString) {
        url += (url.includes("?") ? "&" : "?") + queryString;
      }
    }

    // Add hash
    if (command.hash) {
      url += `#${command.hash}`;
    }

    return url;
  }

  private addToHistory(command: RouterCommand): void {
    if (this.options.enableHistory) {
      this.history.push({
        ...command,
        timestamp: new Date().toISOString(),
      } as any);
      
      // Keep only last 100 commands
      if (this.history.length > 100) {
        this.history = this.history.slice(-100);
      }
    }
  }

  private trackAnalytics(command: RouterCommand): void {
    if (this.options.enableAnalytics) {
      // Track navigation analytics
      console.log("Router Analytics:", {
        type: command.type,
        path: command.path,
        timestamp: new Date().toISOString(),
      });
    }
  }

  execute(command: RouterCommand): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.addToHistory(command);
        this.trackAnalytics(command);

        switch (command.type) {
          case "navigate": {
            const url = this.buildUrl(command);
            this.router.push(url);
            resolve();
            break;
          }

          case "replace": {
            const url = this.buildUrl(command);
            this.router.replace(url);
            resolve();
            break;
          }

          case "back": {
            this.router.back();
            resolve();
            break;
          }

          case "forward": {
            this.router.forward();
            resolve();
            break;
          }

          case "refresh": {
            this.router.refresh();
            resolve();
            break;
          }

          case "external": {
            const url = this.buildUrl(command);
            if (command.newTab) {
              window.open(url, "_blank", "noopener,noreferrer");
            } else {
              window.location.href = url;
            }
            resolve();
            break;
          }

          default:
            reject(new Error(`Unknown router command type: ${command.type}`));
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  // Batch execute multiple commands
  async executeBatch(commands: RouterCommand[]): Promise<void> {
    for (const command of commands) {
      await this.execute(command);
    }
  }

  // Get navigation history
  getHistory(): RouterCommand[] {
    return [...this.history];
  }

  // Clear navigation history
  clearHistory(): void {
    this.history = [];
  }

  // Navigate to a specific route with parameters
  navigateTo(path: string, params?: Record<string, string>, query?: Record<string, string>): Promise<void> {
    return this.execute({
      type: "navigate",
      path,
      params,
      query,
    });
  }

  // Replace current route
  replaceTo(path: string, params?: Record<string, string>, query?: Record<string, string>): Promise<void> {
    return this.execute({
      type: "replace",
      path,
      params,
      query,
    });
  }

  // Navigate to external URL
  openExternal(url: string, newTab: boolean = true): Promise<void> {
    return this.execute({
      type: "external",
      path: url,
      newTab,
    });
  }
}

// Hook for using router engine
export function useRouterEngine(options?: RouterEngineOptions) {
  const router = useRouter();
  
  const routerEngine = new RouterEngine(router, options);

  const executeCommand = useCallback(
    (command: RouterCommand) => routerEngine.execute(command),
    [routerEngine]
  );

  const navigateTo = useCallback(
    (path: string, params?: Record<string, string>, query?: Record<string, string>) =>
      routerEngine.navigateTo(path, params, query),
    [routerEngine]
  );

  const replaceTo = useCallback(
    (path: string, params?: Record<string, string>, query?: Record<string, string>) =>
      routerEngine.replaceTo(path, params, query),
    [routerEngine]
  );

  const openExternal = useCallback(
    (url: string, newTab?: boolean) => routerEngine.openExternal(url, newTab),
    [routerEngine]
  );

  const goBack = useCallback(() => routerEngine.execute({ type: "back" }), [routerEngine]);
  const goForward = useCallback(() => routerEngine.execute({ type: "forward" }), [routerEngine]);
  const refresh = useCallback(() => routerEngine.execute({ type: "refresh" }), [routerEngine]);

  return {
    executeCommand,
    navigateTo,
    replaceTo,
    openExternal,
    goBack,
    goForward,
    refresh,
    getHistory: () => routerEngine.getHistory(),
    clearHistory: () => routerEngine.clearHistory(),
  };
}

// Predefined route commands for common navigation patterns
export const RouteCommands = {
  // Dashboard routes
  dashboard: (): RouterCommand => ({ type: "navigate", path: "/dashboard" }),
  providers: (): RouterCommand => ({ type: "navigate", path: "/providers" }),
  agents: (): RouterCommand => ({ type: "navigate", path: "/agents" }),
  tools: (): RouterCommand => ({ type: "navigate", path: "/tools" }),
  sessions: (): RouterCommand => ({ type: "navigate", path: "/sessions" }),
  
  // Provider specific routes
  providerDetail: (id: string): RouterCommand => ({
    type: "navigate",
    path: "/providers/:id",
    params: { id },
  }),
  
  providerAnalytics: (id?: string): RouterCommand => ({
    type: "navigate",
    path: id ? "/providers/:id/analytics" : "/providers/analytics",
    params: id ? { id } : undefined,
  }),
  
  // Agent routes
  agentDetail: (id: string): RouterCommand => ({
    type: "navigate",
    path: "/agents/:id",
    params: { id },
  }),
  
  agentExecutions: (id: string): RouterCommand => ({
    type: "navigate",
    path: "/agents/:id/executions",
    params: { id },
  }),
  
  // Settings and admin
  settings: (): RouterCommand => ({ type: "navigate", path: "/settings" }),
  profile: (): RouterCommand => ({ type: "navigate", path: "/profile" }),
  
  // External links
  documentation: (): RouterCommand => ({
    type: "external",
    path: "https://docs.synapseai.com",
    newTab: true,
  }),
  
  support: (): RouterCommand => ({
    type: "external",
    path: "https://support.synapseai.com",
    newTab: true,
  }),
};
