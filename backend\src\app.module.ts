import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@nestjs/redis';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { AgentsModule } from './agents/agents.module';
import { ToolsModule } from './tools/tools.module';
import { ProvidersModule } from './providers/providers.module';
import { ApixModule } from './apix/apix.module';
import { UAUIModule } from './uaui/uaui.module';
import { BillingModule } from './billing/billing.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { HealthModule } from './health/health.module';
import { SessionsModule } from './sessions/sessions.module';
import { HITLModule } from './hitl/hitl.module';
import { NotificationsModule } from './notifications/notifications.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    RedisModule.forRoot({
      config: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
      },
    }),
    PrismaModule,
    AuthModule,
    UsersModule,
    AgentsModule,
    ToolsModule,
    ProvidersModule,
    ApixModule,
    UAUIModule,
    BillingModule,
    AnalyticsModule,
    HealthModule,
    SessionsModule,
    HITLModule,
    NotificationsModule,
  ],
})
export class AppModule {}