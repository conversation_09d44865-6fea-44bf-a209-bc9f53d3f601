"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>ton } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  RefreshCw,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Zap,
  DollarSign,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import { useApi } from "@/hooks/useApi";
import { apiClient } from "@/lib/api";
import { Provider, ProviderStatus } from "../../../shared/types";

interface ProviderHealthMetrics {
  id: string;
  name: string;
  type: string;
  status: ProviderStatus;
  isActive: boolean;
  reliability: number;
  averageLatency: number;
  costPerToken: number;
  totalExecutions: number;
  lastUsedAt?: string;
  healthScore: number;
  uptime: number;
  errorRate: number;
  responseTime: number;
  throughput: number;
  lastHealthCheck: string;
  healthTrend: "up" | "down" | "stable";
}

interface HealthCheckResult {
  providerId: string;
  status: "healthy" | "degraded" | "unhealthy";
  responseTime: number;
  error?: string;
  timestamp: string;
}

export default function ProviderHealthMonitoring() {
  const [refreshing, setRefreshing] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [healthChecks, setHealthChecks] = useState<
    Record<string, HealthCheckResult>
  >({});

  // Fetch providers data
  const {
    data: providersResponse,
    loading: providersLoading,
    error: providersError,
    refetch: refetchProviders,
  } = useApi(() => apiClient.getProviders({ isActive: true }));

  // Transform providers data to include health metrics
  const providers: ProviderHealthMetrics[] = React.useMemo(() => {
    if (!providersResponse?.data) return [];

    return providersResponse.data.map((provider: Provider) => {
      const healthCheck = healthChecks[provider.id];
      const healthScore = calculateHealthScore(provider, healthCheck);
      const uptime = calculateUptime(provider);
      const errorRate = calculateErrorRate(provider);
      const healthTrend = calculateHealthTrend(provider);

      return {
        id: provider.id,
        name: provider.name,
        type: provider.type,
        status: provider.status,
        isActive: provider.isActive,
        reliability: provider.reliability,
        averageLatency: provider.averageLatency,
        costPerToken: provider.costPerToken,
        totalExecutions: provider.totalExecutions,
        lastUsedAt: provider.lastUsedAt?.toString(),
        healthScore,
        uptime,
        errorRate,
        responseTime: healthCheck?.responseTime || provider.averageLatency,
        throughput: calculateThroughput(provider),
        lastHealthCheck: healthCheck?.timestamp || new Date().toISOString(),
        healthTrend,
      };
    });
  }, [providersResponse, healthChecks]);

  // Calculate health score based on multiple factors
  const calculateHealthScore = (
    provider: Provider,
    healthCheck?: HealthCheckResult,
  ): number => {
    let score = 0;

    // Status weight (40%)
    if (provider.status === ProviderStatus.ACTIVE && provider.isActive) {
      score += 40;
    } else if (provider.status === ProviderStatus.MAINTENANCE) {
      score += 20;
    }

    // Reliability weight (30%)
    score += (provider.reliability / 100) * 30;

    // Latency weight (20%)
    const latencyScore = Math.max(0, 100 - provider.averageLatency / 10);
    score += (latencyScore / 100) * 20;

    // Health check weight (10%)
    if (healthCheck) {
      if (healthCheck.status === "healthy") {
        score += 10;
      } else if (healthCheck.status === "degraded") {
        score += 5;
      }
    }

    return Math.min(100, Math.max(0, score));
  };

  // Calculate uptime percentage
  const calculateUptime = (provider: Provider): number => {
    // In a real implementation, this would be calculated from historical data
    // For now, we'll derive it from reliability and status
    if (provider.status === ProviderStatus.ACTIVE && provider.isActive) {
      return Math.min(100, provider.reliability + Math.random() * 5);
    }
    return provider.reliability * 0.8;
  };

  // Calculate error rate
  const calculateErrorRate = (provider: Provider): number => {
    // Derive error rate from reliability (inverse relationship)
    return Math.max(0, (100 - provider.reliability) / 10);
  };

  // Calculate throughput (requests per minute)
  const calculateThroughput = (provider: Provider): number => {
    // Estimate based on total executions and average latency
    const avgRequestsPerHour = provider.totalExecutions / (24 * 30); // Rough estimate
    return Math.round(avgRequestsPerHour / 60); // Convert to per minute
  };

  // Calculate health trend
  const calculateHealthTrend = (
    provider: Provider,
  ): "up" | "down" | "stable" => {
    // In a real implementation, this would compare current metrics with historical data
    // For now, we'll use a simple heuristic based on current state
    if (
      provider.reliability > 95 &&
      provider.status === ProviderStatus.ACTIVE
    ) {
      return "up";
    } else if (
      provider.reliability < 80 ||
      provider.status !== ProviderStatus.ACTIVE
    ) {
      return "down";
    }
    return "stable";
  };

  // Perform health check for a specific provider
  const performHealthCheck = async (providerId: string) => {
    try {
      const result = await apiClient.testProvider(providerId);
      const healthCheck: HealthCheckResult = {
        providerId,
        status: result.data.status === "success" ? "healthy" : "unhealthy",
        responseTime: result.data.duration,
        error: result.data.error,
        timestamp: result.data.timestamp,
      };

      setHealthChecks((prev) => ({
        ...prev,
        [providerId]: healthCheck,
      }));
    } catch (error) {
      const healthCheck: HealthCheckResult = {
        providerId,
        status: "unhealthy",
        responseTime: 0,
        error: error instanceof Error ? error.message : "Health check failed",
        timestamp: new Date().toISOString(),
      };

      setHealthChecks((prev) => ({
        ...prev,
        [providerId]: healthCheck,
      }));
    }
  };

  // Refresh all health data
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetchProviders();

      // Perform health checks for all active providers
      const activeProviders = providers.filter((p) => p.isActive);
      await Promise.allSettled(
        activeProviders.map((provider) => performHealthCheck(provider.id)),
      );
    } finally {
      setRefreshing(false);
    }
  };

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!refreshing) {
        handleRefresh();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [refreshing, providers]);

  // Get status badge variant
  const getStatusBadgeVariant = (status: ProviderStatus, isActive: boolean) => {
    if (!isActive) return "secondary";
    switch (status) {
      case ProviderStatus.ACTIVE:
        return "default";
      case ProviderStatus.MAINTENANCE:
        return "outline";
      case ProviderStatus.ERROR:
        return "destructive";
      default:
        return "secondary";
    }
  };

  // Get health score color
  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  // Get health score icon
  const getHealthScoreIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (score >= 70)
      return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <XCircle className="h-4 w-4 text-red-600" />;
  };

  // Get trend icon
  const getTrendIcon = (trend: "up" | "down" | "stable") => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  if (providersError) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load provider health data: {providersError}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6 bg-background">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Provider Health Monitoring
          </h2>
          <p className="text-muted-foreground">
            Real-time health metrics and performance monitoring for AI providers
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={refreshing || providersLoading}
          variant="outline"
        >
          <RefreshCw
            className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Providers
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {providersLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                providers.length
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {providers.filter((p) => p.isActive).length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Healthy Providers
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {providersLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                providers.filter((p) => p.healthScore >= 90).length
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {providers.length > 0
                ? Math.round(
                    (providers.filter((p) => p.healthScore >= 90).length /
                      providers.length) *
                      100,
                  )
                : 0}
              % of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Avg Response Time
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {providersLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                `${Math.round(
                  providers.reduce((sum, p) => sum + p.responseTime, 0) /
                    providers.length || 0,
                )}ms`
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all providers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Executions
            </CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {providersLoading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                providers
                  .reduce((sum, p) => sum + p.totalExecutions, 0)
                  .toLocaleString()
              )}
            </div>
            <p className="text-xs text-muted-foreground">All time total</p>
          </CardContent>
        </Card>
      </div>

      {/* Providers Health Table */}
      <Card>
        <CardHeader>
          <CardTitle>Provider Health Status</CardTitle>
        </CardHeader>
        <CardContent>
          {providersLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Provider</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Health Score</TableHead>
                  <TableHead>Uptime</TableHead>
                  <TableHead>Response Time</TableHead>
                  <TableHead>Error Rate</TableHead>
                  <TableHead>Throughput</TableHead>
                  <TableHead>Trend</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {providers.map((provider) => (
                  <TableRow key={provider.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{provider.name}</div>
                        <div className="text-sm text-muted-foreground capitalize">
                          {provider.type}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={getStatusBadgeVariant(
                          provider.status,
                          provider.isActive,
                        )}
                      >
                        {provider.isActive ? provider.status : "INACTIVE"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getHealthScoreIcon(provider.healthScore)}
                        <span
                          className={getHealthScoreColor(provider.healthScore)}
                        >
                          {Math.round(provider.healthScore)}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={provider.uptime} className="w-16" />
                        <span className="text-sm">
                          {Math.round(provider.uptime)}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span
                        className={
                          provider.responseTime > 1000
                            ? "text-red-600"
                            : "text-green-600"
                        }
                      >
                        {provider.responseTime}ms
                      </span>
                    </TableCell>
                    <TableCell>
                      <span
                        className={
                          provider.errorRate > 5
                            ? "text-red-600"
                            : "text-green-600"
                        }
                      >
                        {provider.errorRate.toFixed(1)}%
                      </span>
                    </TableCell>
                    <TableCell>
                      <span>{provider.throughput} req/min</span>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            {getTrendIcon(provider.healthTrend)}
                          </TooltipTrigger>
                          <TooltipContent>
                            Health trend: {provider.healthTrend}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => performHealthCheck(provider.id)}
                        disabled={refreshing}
                      >
                        Test
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Health Check Results */}
      {Object.keys(healthChecks).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Health Checks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.values(healthChecks)
                .sort(
                  (a, b) =>
                    new Date(b.timestamp).getTime() -
                    new Date(a.timestamp).getTime(),
                )
                .slice(0, 10)
                .map((check) => {
                  const provider = providers.find(
                    (p) => p.id === check.providerId,
                  );
                  return (
                    <div
                      key={`${check.providerId}-${check.timestamp}`}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {check.status === "healthy" ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : check.status === "degraded" ? (
                          <AlertTriangle className="h-5 w-5 text-yellow-600" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-600" />
                        )}
                        <div>
                          <div className="font-medium">
                            {provider?.name || "Unknown Provider"}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {check.error ||
                              `Response time: ${check.responseTime}ms`}
                          </div>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(check.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
