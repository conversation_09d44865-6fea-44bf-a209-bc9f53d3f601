"use client";

import React, { useState, useEffect } from "react";
import { Provider, ProviderStatus, ProviderType } from "../../../shared/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Activity,
  Zap,
  DollarSign,
  Timer,
} from "lucide-react";
import { apiClient } from "@/lib/api";

interface ProviderDetailsProps {
  provider: Provider;
}

export default function ProviderDetails({ provider }: ProviderDetailsProps) {
  const [executions, setExecutions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchExecutions = async () => {
      try {
        setLoading(true);
        // Note: This would need to be implemented in the backend
        // const response = await apiClient.getProviderExecutions(provider.id);
        // if (response.success) {
        //   setExecutions(response.data || []);
        // }
      } catch (err) {
        console.error("Failed to fetch executions:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchExecutions();
  }, [provider.id]);

  const getStatusBadge = (status: ProviderStatus) => {
    const variants = {
      [ProviderStatus.ACTIVE]: {
        variant: "default" as const,
        icon: CheckCircle,
        color: "text-green-500",
      },
      [ProviderStatus.INACTIVE]: {
        variant: "secondary" as const,
        icon: Clock,
        color: "text-gray-500",
      },
      [ProviderStatus.ERROR]: {
        variant: "destructive" as const,
        icon: XCircle,
        color: "text-red-500",
      },
      [ProviderStatus.MAINTENANCE]: {
        variant: "outline" as const,
        icon: AlertCircle,
        color: "text-yellow-500",
      },
    };

    const config = variants[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {status}
      </Badge>
    );
  };

  const getTypeBadge = (type: ProviderType) => {
    const colors = {
      [ProviderType.OPENAI]: "bg-green-100 text-green-800",
      [ProviderType.ANTHROPIC]: "bg-orange-100 text-orange-800",
      [ProviderType.GOOGLE]: "bg-blue-100 text-blue-800",
      [ProviderType.MISTRAL]: "bg-purple-100 text-purple-800",
      [ProviderType.GROQ]: "bg-red-100 text-red-800",
      [ProviderType.CUSTOM]: "bg-gray-100 text-gray-800",
    };

    return (
      <Badge variant="outline" className={colors[type]}>
        {type.toUpperCase()}
      </Badge>
    );
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 4,
    }).format(amount);
  };

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div>
          <h2 className="text-2xl font-bold">{provider.name}</h2>
          {provider.description && (
            <p className="text-muted-foreground mt-1">{provider.description}</p>
          )}
          <div className="flex items-center gap-2 mt-2">
            {getTypeBadge(provider.type)}
            {getStatusBadge(provider.status)}
            {provider.isActive && (
              <Badge variant="outline" className="text-green-600">
                Active
              </Badge>
            )}
          </div>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="models">Models</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  <div className="text-sm font-medium">Reliability</div>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">
                    {Math.round(provider.reliability * 100)}%
                  </div>
                  <Progress
                    value={provider.reliability * 100}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Timer className="h-4 w-4 text-orange-500" />
                  <div className="text-sm font-medium">Avg Latency</div>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">
                    {Math.round(provider.averageLatency)}ms
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Average response time
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-green-500" />
                  <div className="text-sm font-medium">Cost/Token</div>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">
                    {formatCurrency(provider.costPerToken)}
                  </div>
                  <div className="text-xs text-muted-foreground">Per token</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-purple-500" />
                  <div className="text-sm font-medium">Executions</div>
                </div>
                <div className="mt-2">
                  <div className="text-2xl font-bold">
                    {provider.totalExecutions}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Total runs
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Agent Executions</span>
                    <span className="font-medium">
                      {provider._count?.agentExecutions || 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Tool Executions</span>
                    <span className="font-medium">
                      {provider._count?.toolExecutions || 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Priority Level</span>
                    <Badge variant="outline">{provider.priority}/10</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Last Used</span>
                    <span className="text-sm text-muted-foreground">
                      {provider.lastUsedAt
                        ? formatDate(provider.lastUsedAt)
                        : "Never"}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Provider Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Created</span>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(provider.createdAt)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Updated</span>
                    <span className="text-sm text-muted-foreground">
                      {formatDate(provider.updatedAt)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Provider ID</span>
                    <span className="text-sm font-mono text-muted-foreground">
                      {provider.id.slice(0, 8)}...
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Available Models ({provider.models.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {provider.models.map((model) => (
                  <div key={model} className="p-3 border rounded-lg">
                    <div className="font-medium">{model}</div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {provider.capabilities.join(", ")}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Capabilities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {provider.capabilities.map((capability) => (
                  <Badge key={capability} variant="secondary">
                    {capability}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {Math.round(
                      (provider.reliability * 0.4 +
                        (1 - provider.costPerToken) * 0.3 +
                        (1 - provider.averageLatency / 5000) * 0.3) *
                        100,
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Overall Score
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Health Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl mb-2">
                    {provider.reliability > 0.9
                      ? "🟢"
                      : provider.reliability > 0.7
                        ? "🟡"
                        : "🔴"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {provider.reliability > 0.9
                      ? "Excellent"
                      : provider.reliability > 0.7
                        ? "Good"
                        : "Needs Attention"}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Cost Efficiency</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl mb-2">
                    {provider.costPerToken < 0.001
                      ? "💰"
                      : provider.costPerToken < 0.01
                        ? "💸"
                        : "💳"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {provider.costPerToken < 0.001
                      ? "Very Efficient"
                      : provider.costPerToken < 0.01
                        ? "Moderate"
                        : "Expensive"}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Performance charts would be displayed here with real usage data
                over time. This requires historical metrics data from the
                backend.
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="configuration" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuration Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Provider Type</label>
                    <div className="mt-1">{getTypeBadge(provider.type)}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Status</label>
                    <div className="mt-1">
                      {getStatusBadge(provider.status)}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Priority</label>
                    <div className="mt-1 text-sm">{provider.priority}/10</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Active</label>
                    <div className="mt-1">
                      <Badge
                        variant={provider.isActive ? "default" : "secondary"}
                      >
                        {provider.isActive ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                </div>

                {provider.configuration?.baseUrl && (
                  <div>
                    <label className="text-sm font-medium">Base URL</label>
                    <div className="mt-1 text-sm font-mono bg-muted p-2 rounded">
                      {provider.configuration.baseUrl}
                    </div>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium">API Key</label>
                  <div className="mt-1 text-sm text-muted-foreground">
                    ••••••••••••••••••••••••••••••••
                  </div>
                </div>

                {Object.keys(provider.metadata || {}).length > 0 && (
                  <div>
                    <label className="text-sm font-medium">Metadata</label>
                    <div className="mt-1 text-sm font-mono bg-muted p-2 rounded max-h-32 overflow-y-auto">
                      <pre>{JSON.stringify(provider.metadata, null, 2)}</pre>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
