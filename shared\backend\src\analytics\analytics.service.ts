import { Injectable } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";

@Injectable()
export class AnalyticsService {
  constructor(private prisma: PrismaService) {}

  async track(
    eventType: string,
    properties: Record<string, any>,
  ): Promise<void> {
    try {
      await this.prisma.analyticsEvent.create({
        data: {
          type: eventType,
          properties,
          timestamp: new Date(),
          organizationId: properties.organizationId,
          userId: properties.userId,
        },
      });
    } catch (error) {
      // Log error but don't throw to avoid breaking main functionality
      console.error("Analytics tracking error:", error);
    }
  }

  async getEvents(
    organizationId: string,
    eventType?: string,
    startDate?: Date,
    endDate?: Date,
    limit: number = 100,
  ) {
    const where: any = {
      organizationId,
    };

    if (eventType) {
      where.type = eventType;
    }

    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    return this.prisma.analyticsEvent.findMany({
      where,
      take: limit,
      orderBy: {
        timestamp: "desc",
      },
    });
  }

  async getEventCounts(
    organizationId: string,
    groupBy: "type" | "day" | "hour" = "type",
    startDate?: Date,
    endDate?: Date,
  ) {
    const where: any = {
      organizationId,
    };

    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    if (groupBy === "type") {
      return this.prisma.analyticsEvent.groupBy({
        by: ["type"],
        where,
        _count: {
          type: true,
        },
      });
    }

    // For time-based grouping, we'd need more complex queries
    // This is a simplified version
    return this.prisma.analyticsEvent.findMany({
      where,
      select: {
        type: true,
        timestamp: true,
      },
    });
  }
}
