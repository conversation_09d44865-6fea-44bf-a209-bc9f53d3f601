"use client";

import React, { useState } from "react";
import { Agent } from "../../../../shared/types";
import Sidebar from "@/components/layout/Sidebar";
import AgentList from "@/components/agents/AgentList";
import AgentForm from "@/components/agents/AgentForm";
import AgentDetails from "@/components/agents/AgentDetails";
import AgentExecuteDialog from "@/components/agents/AgentExecuteDialog";
import { Toaster } from "@/components/ui/toaster";

type ViewMode = "list" | "create" | "edit" | "details";

export default function AgentsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [executeDialogOpen, setExecuteDialogOpen] = useState(false);
  const [executeAgent, setExecuteAgent] = useState<Agent | null>(null);

  const handleCreateAgent = () => {
    setSelectedAgent(null);
    setViewMode("create");
  };

  const handleEditAgent = (agent: Agent) => {
    setSelectedAgent(agent);
    setViewMode("edit");
  };

  const handleViewAgent = (agent: Agent) => {
    setSelectedAgent(agent);
    setViewMode("details");
  };

  const handleExecuteAgent = (agent: Agent) => {
    setExecuteAgent(agent);
    setExecuteDialogOpen(true);
  };

  const handleSaveAgent = (agent: Agent) => {
    setViewMode("list");
    setSelectedAgent(null);
  };

  const handleCancelForm = () => {
    setViewMode("list");
    setSelectedAgent(null);
  };

  const renderContent = () => {
    switch (viewMode) {
      case "create":
        return (
          <AgentForm
            onSave={handleSaveAgent}
            onCancel={handleCancelForm}
          />
        );
      case "edit":
        return (
          <AgentForm
            agent={selectedAgent!}
            onSave={handleSaveAgent}
            onCancel={handleCancelForm}
          />
        );
      case "details":
        return (
          <AgentDetails
            agentId={selectedAgent!.id}
            onEdit={handleEditAgent}
            onExecute={handleExecuteAgent}
          />
        );
      default:
        return (
          <AgentList
            onCreateAgent={handleCreateAgent}
            onEditAgent={handleEditAgent}
            onViewAgent={handleViewAgent}
            onExecuteAgent={handleExecuteAgent}
          />
        );
    }
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar Navigation */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="container mx-auto p-6">
          {/* Breadcrumb */}
          <div className="mb-6">
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="inline-flex items-center space-x-1 md:space-x-3">
                <li className="inline-flex items-center">
                  <button
                    onClick={() => setViewMode("list")}
                    className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
                  >
                    Agents
                  </button>
                </li>
                {viewMode !== "list" && (
                  <li>
                    <div className="flex items-center">
                      <svg
                        className="w-3 h-3 text-gray-400 mx-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 6 10"
                      >
                        <path
                          stroke="currentColor"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="m1 9 4-4-4-4"
                        />
                      </svg>
                      <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                        {viewMode === "create" && "Create Agent"}
                        {viewMode === "edit" && `Edit ${selectedAgent?.name}`}
                        {viewMode === "details" && selectedAgent?.name}
                      </span>
                    </div>
                  </li>
                )}
              </ol>
            </nav>
          </div>

          {/* Page Content */}
          {renderContent()}
        </div>
      </div>

      {/* Execute Dialog */}
      <AgentExecuteDialog
        agent={executeAgent}
        open={executeDialogOpen}
        onOpenChange={setExecuteDialogOpen}
      />

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
}
