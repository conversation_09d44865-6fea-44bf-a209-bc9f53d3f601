"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>ton } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  CheckCircle,
  XCircle,
  Clock,
  Search,
  Filter,
  RefreshCw,
  Calendar,
  Zap,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
} from "lucide-react";
import { apiClient } from "@/lib/api";
import { useApi } from "@/hooks/useApi";
import { formatDistanceToNow } from "date-fns";

interface ExecutionRecord {
  id: string;
  providerId: string;
  providerName: string;
  status: "SUCCESS" | "FAILED" | "TIMEOUT" | "CANCELLED";
  responseTime: number;
  inputTokens: number;
  outputTokens: number;
  cost: number;
  model: string;
  errorMessage?: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

interface ExecutionStats {
  totalExecutions: number;
  successRate: number;
  averageResponseTime: number;
  totalCost: number;
  totalTokens: number;
}

interface ProviderExecutionHistoryProps {
  providerId?: string;
  showProviderFilter?: boolean;
}

export default function ProviderExecutionHistory({ 
  providerId, 
  showProviderFilter = true 
}: ProviderExecutionHistoryProps) {
  const [selectedProvider, setSelectedProvider] = useState<string>(providerId || "all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [timeRange, setTimeRange] = useState("7d");

  // Fetch providers for filter
  const { data: providersData } = useApi(
    () => apiClient.getProviders({ isActive: true }),
    []
  );

  // Fetch execution history
  const { 
    data: executionsData, 
    loading: executionsLoading, 
    error: executionsError,
    refetch: refetchExecutions 
  } = useApi(
    () => apiClient.getProviderExecutions(
      selectedProvider === "all" ? undefined : selectedProvider,
      {
        page,
        limit: pageSize,
        status: statusFilter === "all" ? undefined : statusFilter,
        search: searchTerm || undefined,
        timeRange,
      }
    ),
    [selectedProvider, page, pageSize, statusFilter, searchTerm, timeRange]
  );

  const executions: ExecutionRecord[] = executionsData?.data?.executions || [];
  const stats: ExecutionStats = executionsData?.data?.stats || {
    totalExecutions: 0,
    successRate: 0,
    averageResponseTime: 0,
    totalCost: 0,
    totalTokens: 0,
  };
  const totalPages = Math.ceil((executionsData?.data?.total || 0) / pageSize);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return <Badge className="bg-green-100 text-green-800 border-green-200">Success</Badge>;
      case "FAILED":
        return <Badge className="bg-red-100 text-red-800 border-red-200">Failed</Badge>;
      case "TIMEOUT":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Timeout</Badge>;
      case "CANCELLED":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "FAILED":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "TIMEOUT":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "CANCELLED":
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatResponseTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatCost = (cost: number) => {
    return `$${cost.toFixed(4)}`;
  };

  const handleRefresh = () => {
    refetchExecutions();
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page when searching
  };

  const handleFilterChange = (filter: string, value: string) => {
    if (filter === "provider") {
      setSelectedProvider(value);
    } else if (filter === "status") {
      setStatusFilter(value);
    } else if (filter === "timeRange") {
      setTimeRange(value);
    }
    setPage(1); // Reset to first page when filtering
  };

  if (executionsError) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to load execution history: {executionsError}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Execution History</h2>
          <p className="text-muted-foreground">
            Provider execution logs and performance metrics
          </p>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-blue-500" />
              <div className="text-sm font-medium">Total Executions</div>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{stats.totalExecutions.toLocaleString()}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div className="text-sm font-medium">Success Rate</div>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{stats.successRate.toFixed(1)}%</div>
              <div className="flex items-center text-xs">
                {stats.successRate >= 95 ? (
                  <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
                )}
                {stats.successRate >= 95 ? "Excellent" : "Needs attention"}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-500" />
              <div className="text-sm font-medium">Avg Response Time</div>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{formatResponseTime(stats.averageResponseTime)}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <span className="h-4 w-4 text-purple-500">💰</span>
              <div className="text-sm font-medium">Total Cost</div>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{formatCost(stats.totalCost)}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <span className="h-4 w-4 text-indigo-500">🔤</span>
              <div className="text-sm font-medium">Total Tokens</div>
            </div>
            <div className="mt-2">
              <div className="text-2xl font-bold">{stats.totalTokens.toLocaleString()}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search executions..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {showProviderFilter && (
              <Select value={selectedProvider} onValueChange={(value) => handleFilterChange("provider", value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Providers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Providers</SelectItem>
                  {providersData?.data?.map((provider: any) => (
                    <SelectItem key={provider.id} value={provider.id}>
                      {provider.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            <Select value={statusFilter} onValueChange={(value) => handleFilterChange("status", value)}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="SUCCESS">Success</SelectItem>
                <SelectItem value="FAILED">Failed</SelectItem>
                <SelectItem value="TIMEOUT">Timeout</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={timeRange} onValueChange={(value) => handleFilterChange("timeRange", value)}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">Last Hour</SelectItem>
                <SelectItem value="24h">Last 24h</SelectItem>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Execution History Table */}
      <Card>
        <CardHeader>
          <CardTitle>Execution Records</CardTitle>
        </CardHeader>
        <CardContent>
          {executionsLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : executions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <div className="text-lg font-medium mb-2">No executions found</div>
              <div className="text-sm">
                {searchTerm || statusFilter !== "all" || selectedProvider !== "all"
                  ? "Try adjusting your filters or search terms"
                  : "No execution history available for the selected time range"}
              </div>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Status</TableHead>
                      <TableHead>Provider</TableHead>
                      <TableHead>Model</TableHead>
                      <TableHead>Response Time</TableHead>
                      <TableHead>Tokens</TableHead>
                      <TableHead>Cost</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {executions.map((execution) => (
                      <TableRow key={execution.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(execution.status)}
                            {getStatusBadge(execution.status)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{execution.providerName}</div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{execution.model}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="font-mono text-sm">
                            {formatResponseTime(execution.responseTime)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>In: {execution.inputTokens.toLocaleString()}</div>
                            <div>Out: {execution.outputTokens.toLocaleString()}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-mono text-sm">
                            {formatCost(execution.cost)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <div className="text-sm text-muted-foreground">
                                  {formatDistanceToNow(new Date(execution.createdAt), { addSuffix: true })}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                {new Date(execution.createdAt).toLocaleString()}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {execution.errorMessage && (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <AlertTriangle className="h-4 w-4 text-red-500" />
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-xs">
                                    <div className="text-sm">{execution.errorMessage}</div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((page - 1) * pageSize) + 1} to {Math.min(page * pageSize, executionsData?.data?.total || 0)} of {executionsData?.data?.total || 0} executions
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(page - 1)}
                      disabled={page <= 1}
                    >
                      Previous
                    </Button>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const pageNum = Math.max(1, Math.min(totalPages - 4, page - 2)) + i;
                        return (
                          <Button
                            key={pageNum}
                            variant={pageNum === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => setPage(pageNum)}
                            className="w-8 h-8 p-0"
                          >
                            {pageNum}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(page + 1)}
                      disabled={page >= totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
