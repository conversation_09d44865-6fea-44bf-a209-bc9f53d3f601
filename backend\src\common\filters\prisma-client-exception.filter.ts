import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
  Logger,
} from "@nestjs/common";
import { Response } from "express";
import { Prisma } from "@prisma/client";

@Catch(Prisma.PrismaClientKnownRequestError)
export class PrismaClientExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(PrismaClientExceptionFilter.name);

  catch(exception: Prisma.PrismaClientKnownRequestError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = "Internal server error";
    let code = "INTERNAL_ERROR";

    switch (exception.code) {
      case "P2000":
        status = HttpStatus.BAD_REQUEST;
        message =
          "The provided value for the column is too long for the column type";
        code = "VALUE_TOO_LONG";
        break;
      case "P2001":
        status = HttpStatus.NOT_FOUND;
        message =
          "The record searched for in the where condition does not exist";
        code = "RECORD_NOT_FOUND";
        break;
      case "P2002":
        status = HttpStatus.CONFLICT;
        message = this.getUniqueConstraintMessage(exception);
        code = "UNIQUE_CONSTRAINT_VIOLATION";
        break;
      case "P2003":
        status = HttpStatus.BAD_REQUEST;
        message = "Foreign key constraint failed on the field";
        code = "FOREIGN_KEY_CONSTRAINT";
        break;
      case "P2004":
        status = HttpStatus.BAD_REQUEST;
        message = "A constraint failed on the database";
        code = "CONSTRAINT_FAILED";
        break;
      case "P2005":
        status = HttpStatus.BAD_REQUEST;
        message =
          "The value stored in the database for the field is invalid for the field type";
        code = "INVALID_FIELD_VALUE";
        break;
      case "P2006":
        status = HttpStatus.BAD_REQUEST;
        message = "The provided value for the field is not valid";
        code = "INVALID_VALUE";
        break;
      case "P2007":
        status = HttpStatus.BAD_REQUEST;
        message = "Data validation error";
        code = "VALIDATION_ERROR";
        break;
      case "P2008":
        status = HttpStatus.BAD_REQUEST;
        message = "Failed to parse the query";
        code = "QUERY_PARSE_ERROR";
        break;
      case "P2009":
        status = HttpStatus.BAD_REQUEST;
        message = "Failed to validate the query";
        code = "QUERY_VALIDATION_ERROR";
        break;
      case "P2010":
        status = HttpStatus.INTERNAL_SERVER_ERROR;
        message = "Raw query failed";
        code = "RAW_QUERY_FAILED";
        break;
      case "P2011":
        status = HttpStatus.BAD_REQUEST;
        message = "Null constraint violation on the field";
        code = "NULL_CONSTRAINT_VIOLATION";
        break;
      case "P2012":
        status = HttpStatus.BAD_REQUEST;
        message = "Missing a required value";
        code = "MISSING_REQUIRED_VALUE";
        break;
      case "P2013":
        status = HttpStatus.BAD_REQUEST;
        message = "Missing the required argument for field";
        code = "MISSING_REQUIRED_ARGUMENT";
        break;
      case "P2014":
        status = HttpStatus.BAD_REQUEST;
        message =
          "The change you are trying to make would violate the required relation";
        code = "RELATION_VIOLATION";
        break;
      case "P2015":
        status = HttpStatus.NOT_FOUND;
        message = "A related record could not be found";
        code = "RELATED_RECORD_NOT_FOUND";
        break;
      case "P2016":
        status = HttpStatus.BAD_REQUEST;
        message = "Query interpretation error";
        code = "QUERY_INTERPRETATION_ERROR";
        break;
      case "P2017":
        status = HttpStatus.BAD_REQUEST;
        message = "The records for relation are not connected";
        code = "RECORDS_NOT_CONNECTED";
        break;
      case "P2018":
        status = HttpStatus.NOT_FOUND;
        message = "The required connected records were not found";
        code = "CONNECTED_RECORDS_NOT_FOUND";
        break;
      case "P2019":
        status = HttpStatus.BAD_REQUEST;
        message = "Input error";
        code = "INPUT_ERROR";
        break;
      case "P2020":
        status = HttpStatus.BAD_REQUEST;
        message = "Value out of range for the type";
        code = "VALUE_OUT_OF_RANGE";
        break;
      case "P2021":
        status = HttpStatus.NOT_FOUND;
        message = "The table does not exist in the current database";
        code = "TABLE_NOT_FOUND";
        break;
      case "P2022":
        status = HttpStatus.NOT_FOUND;
        message = "The column does not exist in the current database";
        code = "COLUMN_NOT_FOUND";
        break;
      case "P2023":
        status = HttpStatus.BAD_REQUEST;
        message = "Inconsistent column data";
        code = "INCONSISTENT_COLUMN_DATA";
        break;
      case "P2024":
        status = HttpStatus.REQUEST_TIMEOUT;
        message =
          "Timed out fetching a new connection from the connection pool";
        code = "CONNECTION_POOL_TIMEOUT";
        break;
      case "P2025":
        status = HttpStatus.NOT_FOUND;
        message =
          "An operation failed because it depends on one or more records that were required but not found";
        code = "DEPENDENT_RECORDS_NOT_FOUND";
        break;
      case "P2026":
        status = HttpStatus.BAD_REQUEST;
        message =
          "The current database provider doesn't support a feature that the query used";
        code = "UNSUPPORTED_FEATURE";
        break;
      case "P2027":
        status = HttpStatus.INTERNAL_SERVER_ERROR;
        message =
          "Multiple errors occurred on the database during query execution";
        code = "MULTIPLE_ERRORS";
        break;
      case "P2028":
        status = HttpStatus.INTERNAL_SERVER_ERROR;
        message = "Transaction API error";
        code = "TRANSACTION_API_ERROR";
        break;
      case "P2030":
        status = HttpStatus.NOT_FOUND;
        message = "Cannot find a fulltext index to use for the search";
        code = "FULLTEXT_INDEX_NOT_FOUND";
        break;
      case "P2033":
        status = HttpStatus.BAD_REQUEST;
        message =
          "A number used in the query does not fit into a 64 bit signed integer";
        code = "NUMBER_OUT_OF_RANGE";
        break;
      case "P2034":
        status = HttpStatus.CONFLICT;
        message = "Transaction failed due to a write conflict or a deadlock";
        code = "TRANSACTION_CONFLICT";
        break;
      default:
        this.logger.error(
          `Unhandled Prisma error code: ${exception.code}`,
          exception,
        );
        break;
    }

    // Log the error for monitoring
    this.logger.error(`Prisma error ${exception.code}: ${message}`, {
      code: exception.code,
      meta: exception.meta,
      clientVersion: exception.clientVersion,
      url: request.url,
      method: request.method,
      userId: request.user?.id,
      organizationId: request.user?.organizationId,
    });

    response.status(status).json({
      success: false,
      error: {
        code,
        message,
        details:
          process.env.NODE_ENV === "development" ? exception.meta : undefined,
      },
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }

  private getUniqueConstraintMessage(
    exception: Prisma.PrismaClientKnownRequestError,
  ): string {
    const target = exception.meta?.target as string[];
    if (target && target.length > 0) {
      const field = target[0];
      return `A record with this ${field} already exists`;
    }
    return "A record with these values already exists";
  }
}

@Catch(Prisma.PrismaClientUnknownRequestError)
export class PrismaClientUnknownExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(PrismaClientUnknownExceptionFilter.name);

  catch(
    exception: Prisma.PrismaClientUnknownRequestError,
    host: ArgumentsHost,
  ) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    this.logger.error("Unknown Prisma error:", exception);

    response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: {
        code: "UNKNOWN_DATABASE_ERROR",
        message: "An unknown database error occurred",
        details:
          process.env.NODE_ENV === "development"
            ? exception.message
            : undefined,
      },
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}

@Catch(Prisma.PrismaClientRustPanicError)
export class PrismaClientRustPanicExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(
    PrismaClientRustPanicExceptionFilter.name,
  );

  catch(exception: Prisma.PrismaClientRustPanicError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    this.logger.error("Prisma Rust panic:", exception);

    response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      error: {
        code: "DATABASE_PANIC",
        message: "A critical database error occurred",
      },
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}

@Catch(Prisma.PrismaClientInitializationError)
export class PrismaClientInitializationExceptionFilter
  implements ExceptionFilter
{
  private readonly logger = new Logger(
    PrismaClientInitializationExceptionFilter.name,
  );

  catch(
    exception: Prisma.PrismaClientInitializationError,
    host: ArgumentsHost,
  ) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    this.logger.error("Prisma initialization error:", exception);

    response.status(HttpStatus.SERVICE_UNAVAILABLE).json({
      success: false,
      error: {
        code: "DATABASE_UNAVAILABLE",
        message: "Database service is currently unavailable",
      },
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}

@Catch(Prisma.PrismaClientValidationError)
export class PrismaClientValidationExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(
    PrismaClientValidationExceptionFilter.name,
  );

  catch(exception: Prisma.PrismaClientValidationError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    this.logger.error("Prisma validation error:", exception.message);

    response.status(HttpStatus.BAD_REQUEST).json({
      success: false,
      error: {
        code: "VALIDATION_ERROR",
        message: "Request validation failed",
        details:
          process.env.NODE_ENV === "development"
            ? exception.message
            : undefined,
      },
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
