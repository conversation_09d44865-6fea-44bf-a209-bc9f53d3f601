// This is your Prisma schema file,
// Template system for reusable prompts and configurations
model Template {
  id             String         @id @default(cuid())
  name           String
  description    String?
  category       String?
  type           TemplateType
  content        Json
  variables      Json           @default("[]")
  status         TemplateStatus @default(DRAFT)
  version        Int            @default(1)
  isPublic       Boolean        @default(false)
  tags           String[]       @default([])
  rating         Float?         @default(0)
  usageCount     Int            @default(0)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  userId         String
  organizationId String
  parentId       String?

  // Relations
  user         User              @relation(fields: [userId], references: [id])
  organization Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  parent       Template?         @relation("TemplateVersions", fields: [parentId], references: [id])
  versions     Template[]        @relation("TemplateVersions")
  agents       Agent[]

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([category])
  @@map("templates")
}

enum TemplateType {
  AGENT_PROMPT
  SYSTEM_PROMPT
  TOOL_CONFIG
  WORKFLOW
  WIDGET
}

enum TemplateStatus {
  DRAFT
  ACTIVE
  ARCHIVED
  DEPRECATED
}

// Workflow system for complex multi-step processes
model Workflow {
  id             String         @id @default(cuid())
  name           String
  description    String?
  definition     Json
  status         WorkflowStatus @default(DRAFT)
  version        Int            @default(1)
  tags           String[]       @default([])
  metadata       Json           @default("{}")
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  userId         String
  organizationId String

  // Relations
  user         User               @relation(fields: [userId], references: [id])
  organization Organization       @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  tools        WorkflowTool[]
  sessions     Session[]
  executions   WorkflowExecution[]

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@map("workflows")
}

enum WorkflowStatus {
  DRAFT
  ACTIVE
  ARCHIVED
}

model WorkflowTool {
  id         String @id @default(cuid())
  workflowId String
  toolId     String
  order      Int    @default(0)
  config     Json   @default("{}")

  workflow Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  tool     Tool     @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([workflowId, toolId])
  @@map("workflow_tools")
}

// Provider management for AI services
model Provider {
  id             String         @id @default(cuid())
  name           String
  type           ProviderType
  config         Json
  credentials    Json
  status         ProviderStatus @default(ACTIVE)
  priority       Int            @default(0)
  costPerToken   Float?
  rateLimit      Json           @default("{}")
  metadata       Json           @default("{}")
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organizationId String

  // Relations
  organization Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  usage        ProviderUsage[]

  @@index([organizationId])
  @@index([status])
  @@index([type])
  @@map("providers")
}

enum ProviderType {
  OPENAI
  CLAUDE
  GEMINI
  MISTRAL
  GROQ
  CUSTOM
}

enum ProviderStatus {
  ACTIVE
  INACTIVE
  ERROR
  MAINTENANCE
}

model ProviderUsage {
  id          String   @id @default(cuid())
  providerId  String
  tokens      Int
  cost        Float
  latency     Int?
  success     Boolean
  error       String?
  metadata    Json     @default("{}")
  timestamp   DateTime @default(now())
  executionId String?

  // Relations
  provider Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@index([providerId])
  @@index([timestamp])
  @@map("provider_usage")
}

// HITL Decision tracking
model HITLDecision {
  id            String      @id @default(cuid())
  requestId     String
  decision      HITLDecision
  reasoning     String?
  data          Json        @default("{}")
  createdAt     DateTime    @default(now())
  userId        String

  // Relations
  request HITLRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)
  user    User        @relation(fields: [userId], references: [id])

  @@index([requestId])
  @@index([userId])
  @@map("hitl_decisions")
}

enum HITLDecisionType {
  APPROVED
  REJECTED
  ESCALATED
  MODIFIED
}

// Document chunks for vector search
model DocumentChunk {
  id             String    @id @default(cuid())
  documentId     String
  content        String
  embedding      Unsupported("vector(1536)")?
  metadata       Json      @default("{}")
  chunkIndex     Int
  tokenCount     Int?
  createdAt      DateTime  @default(now())
  organizationId String

  // Relations
  document     Document     @relation(fields: [documentId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([documentId])
  @@index([organizationId])
  @@map("document_chunks")
}

// Knowledge search tracking
model KnowledgeSearch {
  id             String   @id @default(cuid())
  query          String
  results        Json
  documentIds    String[]
  metadata       Json     @default("{}")
  timestamp      DateTime @default(now())
  organizationId String
  userId         String?
  sessionId      String?

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  documents    Document[]  @relation(fields: [documentIds], references: [id])

  @@index([organizationId])
  @@index([timestamp])
  @@map("knowledge_searches")
}

// Widget execution tracking
model WidgetExecution {
  id          String   @id @default(cuid())
  widgetId    String
  input       Json
  output      Json?
  status      String
  error       String?
  duration    Int?
  userAgent   String?
  ipAddress   String?
  metadata    Json     @default("{}")
  createdAt   DateTime @default(now())
  completedAt DateTime?

  // Relations
  widget Widget @relation(fields: [widgetId], references: [id], onDelete: Cascade)

  @@index([widgetId])
  @@index([createdAt])
  @@map("widget_executions")
}

// Analytics and metrics
model Analytics {
  id             String   @id @default(cuid())
  metric         String
  value          Float
  dimensions     Json     @default("{}")
  timestamp      DateTime @default(now())
  organizationId String
  userId         String?
  sessionId      String?

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([metric])
  @@index([timestamp])
  @@map("analytics")
}

// Metrics aggregation
model Metrics {
  id             String   @id @default(cuid())
  name           String
  value          Float
  unit           String?
  tags           Json     @default("{}")
  timestamp      DateTime @default(now())
  organizationId String?

  @@index([name])
  @@index([timestamp])
  @@index([organizationId])
  @@map("metrics")
}

// Sandbox environments for testing
model Sandbox {
  id             String        @id @default(cuid())
  name           String
  description    String?
  config         Json
  status         SandboxStatus @default(ACTIVE)
  resources      Json          @default("{}")
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  expiresAt      DateTime?
  userId         String
  organizationId String

  // Relations
  user         User         @relation(fields: [userId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  testResults  TestResult[]

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@map("sandboxes")
}

enum SandboxStatus {
  ACTIVE
  PAUSED
  EXPIRED
  TERMINATED
}

// Test results
model TestResult {
  id          String   @id @default(cuid())
  sandboxId   String
  testName    String
  status      String
  input       Json
  output      Json?
  error       String?
  duration    Int?
  assertions  Json     @default("[]")
  metadata    Json     @default("{}")
  createdAt   DateTime @default(now())
  completedAt DateTime?
  userId      String

  // Relations
  sandbox Sandbox @relation(fields: [sandboxId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id])

  @@index([sandboxId])
  @@index([userId])
  @@index([status])
  @@map("test_results")
}

// Notification preferences
model NotificationPreference {
  id             String              @id @default(cuid())
  userId         String
  organizationId String
  type           NotificationType
  channel        NotificationChannel
  enabled        Boolean             @default(true)
  settings       Json                @default("{}")
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([userId, type, channel])
  @@index([userId])
  @@index([organizationId])
  @@map("notification_preferences")
}

// Workflow execution tracking
model WorkflowExecution {
  id          String   @id @default(cuid())
  workflowId  String
  status      String
  input       Json
  output      Json?
  error       String?
  steps       Json     @default("[]")
  duration    Int?
  cost        Float?
  createdAt   DateTime @default(now())
  completedAt DateTime?

  // Relations
  workflow Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)

  @@index([workflowId])
  @@index([status])
  @@index([createdAt])
  @@map("workflow_executions")
}


// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"]
  previewFeatures = ["fullTextSearch", "postgresqlExtensions"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  extensions = [vector]
}

// Organizations for multi-tenancy
model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  settings    Json     @default("{}")
  plan        String   @default("free")
  status      OrganizationStatus @default(ACTIVE)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users               User[]
  agents              Agent[]
  tools               Tool[]
  hybrids             Hybrid[]
  workflows           Workflow[]
  sessions            Session[]
  documents           Document[]
  documentChunks      DocumentChunk[]
  widgets             Widget[]
  notifications       Notification[]
  billingUsage        BillingUsage[]
  quotas              Quota[]
  templates           Template[]
  providers           Provider[]
  hitlRequests        HITLRequest[]
  knowledgeSearches   KnowledgeSearch[]
  analytics           Analytics[]
  sandboxes           Sandbox[]
  notificationPrefs   NotificationPreference[]

  @@index([slug])
  @@index([status])
  @@map("organizations")
}

enum OrganizationStatus {
  ACTIVE
  SUSPENDED
  CANCELLED
}

// Users with role-based access control
model User {
  id             String   @id @default(cuid())
  email          String   @unique
  passwordHash   String?
  firstName      String
  lastName       String
  avatar         String?
  role           UserRole @default(VIEWER)
  isActive       Boolean  @default(true)
  lastLoginAt    DateTime?
  preferences    Json     @default("{}")
  apiKeys        Json     @default("[]")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  organizationId String

  // Relations
  organization        Organization           @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  sessions            Session[]
  agents              Agent[]
  tools               Tool[]
  hybrids             Hybrid[]
  workflows           Workflow[]
  hitlRequests        HITLRequest[]
  hitlDecisions       HITLDecision[]
  notifications       Notification[]
  templates           Template[]
  sandboxes           Sandbox[]
  testResults         TestResult[]
  notificationPrefs   NotificationPreference[]

  @@index([email])
  @@index([organizationId])
  @@index([role])
  @@map("users")
}

enum UserRole {
  SUPER_ADMIN
  ORG_ADMIN
  DEVELOPER
  VIEWER
}

// AI Agents
model Agent {
  id             String      @id @default(cuid())
  name           String
  description    String?
  prompt         String
  model          String
  temperature    Float       @default(0.7)
  maxTokens      Int         @default(1000)
  systemPrompt   String?
  status         AgentStatus @default(DRAFT)
  version        Int         @default(1)
  isPublic       Boolean     @default(false)
  tags           String[]    @default([])
  metadata       Json        @default("{}")
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  userId         String
  organizationId String
  templateId     String?

  // Relations
  user         User           @relation(fields: [userId], references: [id])
  organization Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  template     Template?      @relation(fields: [templateId], references: [id])
  sessions     Session[]
  hybrids      HybridAgent[]
  widgets      Widget[]
  executions   AgentExecution[]

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@index([templateId])
  @@map("agents")
}

enum AgentStatus {
  DRAFT
  ACTIVE
  ARCHIVED
}

// Tools for API integrations
model Tool {
  id             String     @id @default(cuid())
  name           String
  description    String?
  schema         Json
  endpoint       String?
  method         String     @default("POST")
  headers        Json       @default("{}")
  authentication Json       @default("{}")
  status         ToolStatus @default(DRAFT)
  version        Int        @default(1)
  isPublic       Boolean    @default(false)
  tags           String[]   @default([])
  category       String?
  metadata       Json       @default("{}")
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  userId         String
  organizationId String

  // Relations
  user         User          @relation(fields: [userId], references: [id])
  organization Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  hybrids      HybridTool[]
  workflows    WorkflowTool[]
  executions   ToolExecution[]

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@index([category])
  @@map("tools")
}

enum ToolStatus {
  DRAFT
  ACTIVE
  ARCHIVED
}

// Hybrid workflows combining agents and tools
model Hybrid {
  id             String       @id @default(cuid())
  name           String
  description    String?
  workflow       Json
  status         HybridStatus @default(DRAFT)
  version        Int          @default(1)
  tags           String[]     @default([])
  metadata       Json         @default("{}")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  userId         String
  organizationId String

  // Relations
  user         User           @relation(fields: [userId], references: [id])
  organization Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  agents       HybridAgent[]
  tools        HybridTool[]
  sessions     Session[]
  executions   HybridExecution[]

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@map("hybrids")
}

enum HybridStatus {
  DRAFT
  ACTIVE
  ARCHIVED
}

// Junction tables for hybrid relationships
model HybridAgent {
  id       String @id @default(cuid())
  hybridId String
  agentId  String
  order    Int    @default(0)
  config   Json   @default("{}")

  hybrid Agent @relation(fields: [hybridId], references: [id], onDelete: Cascade)
  agent  Hybrid @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@unique([hybridId, agentId])
  @@map("hybrid_agents")
}

model HybridTool {
  id       String @id @default(cuid())
  hybridId String
  toolId   String
  order    Int    @default(0)
  config   Json   @default("{}")

  hybrid Hybrid @relation(fields: [hybridId], references: [id], onDelete: Cascade)
  tool   Tool   @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([hybridId, toolId])
  @@map("hybrid_tools")
}

// Sessions for conversation memory
model Session {
  id             String        @id @default(cuid())
  title          String?
  context        Json          @default("{}")
  memory         Json          @default("{}")
  status         SessionStatus @default(ACTIVE)
  metadata       Json          @default("{}")
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  userId         String
  organizationId String
  agentId        String?
  hybridId       String?
  workflowId     String?

  // Relations
  user         User         @relation(fields: [userId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  agent        Agent?       @relation(fields: [agentId], references: [id])
  hybrid       Hybrid?      @relation(fields: [hybridId], references: [id])
  workflow     Workflow?    @relation(fields: [workflowId], references: [id])
  messages     Message[]
  hitlRequests HITLRequest[]

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@index([agentId])
  @@index([hybridId])
  @@index([workflowId])
  @@map("sessions")
}

enum SessionStatus {
  ACTIVE
  PAUSED
  COMPLETED
  ARCHIVED
}

// Messages within sessions
model Message {
  id        String      @id @default(cuid())
  content   String
  role      MessageRole
  metadata  Json        @default("{}")
  createdAt DateTime    @default(now())
  sessionId String

  // Relations
  session Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("messages")
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
  TOOL
}

// Human-in-the-loop requests
model HITLRequest {
  id             String           @id @default(cuid())
  type           HITLRequestType
  title          String
  description    String?
  data           Json             @default("{}")
  status         HITLStatus       @default(PENDING)
  priority       HITLPriority     @default(MEDIUM)
  response       Json?
  responseAt     DateTime?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  userId         String
  assigneeId     String?
  sessionId      String?
  organizationId String

  // Relations
  user         User           @relation(fields: [userId], references: [id])
  organization Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  session      Session?       @relation(fields: [sessionId], references: [id])
  decisions    HITLDecision[]

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@index([priority])
  @@map("hitl_requests")
}

enum HITLRequestType {
  APPROVAL
  REVIEW
  CONSULTATION
  ESCALATION
}

enum HITLStatus {
  PENDING
  APPROVED
  REJECTED
  ESCALATED
  COMPLETED
}

enum HITLPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// Knowledge base documents
model Document {
  id             String         @id @default(cuid())
  title          String
  content        String
  type           DocumentType
  url            String?
  size           Int?
  hash           String?
  metadata       Json           @default("{}")
  status         DocumentStatus @default(PROCESSING)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organizationId String

  // Relations
  organization Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  chunks       DocumentChunk[]
  searches     KnowledgeSearch[]

  @@index([organizationId])
  @@index([status])
  @@index([type])
  @@index([hash])
  @@map("documents")
}

enum DocumentType {
  PDF
  DOCX
  TXT
  URL
  DATABASE
}

enum DocumentStatus {
  PROCESSING
  INDEXED
  FAILED
}

// Embeddable widgets
model Widget {
  id             String       @id @default(cuid())
  name           String
  description    String?
  type           WidgetType
  config         Json         @default("{}")
  theme          Json         @default("{}")
  status         WidgetStatus @default(DRAFT)
  embedCode      String?
  domain         String?
  analytics      Json         @default("{}")
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  agentId        String?
  organizationId String

  // Relations
  agent        Agent?         @relation(fields: [agentId], references: [id])
  organization Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  executions   WidgetExecution[]

  @@index([organizationId])
  @@index([status])
  @@index([type])
  @@map("widgets")
}

enum WidgetType {
  CHAT
  FORM
  ASSISTANT
  CUSTOM
}

enum WidgetStatus {
  DRAFT
  ACTIVE
  ARCHIVED
}

// Notifications
model Notification {
  id             String             @id @default(cuid())
  title          String
  message        String
  type           NotificationType
  channel        NotificationChannel
  status         NotificationStatus @default(PENDING)
  data           Json               @default("{}")
  priority       NotificationPriority @default(MEDIUM)
  retryCount     Int                @default(0)
  sentAt         DateTime?
  deliveredAt    DateTime?
  createdAt      DateTime           @default(now())
  userId         String
  organizationId String

  // Relations
  user         User         @relation(fields: [userId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([priority])
  @@map("notifications")
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
}

enum NotificationChannel {
  EMAIL
  SMS
  WEBHOOK
  PUSH
  SLACK
  TEAMS
}

enum NotificationStatus {
  PENDING
  SENT
  FAILED
  DELIVERED
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// Billing and usage tracking
model BillingUsage {
  id             String     @id @default(cuid())
  resource       String
  quantity       Float
  unit           String
  cost           Float
  metadata       Json       @default("{}")
  timestamp      DateTime   @default(now())
  organizationId String
  userId         String?
  sessionId      String?
  executionId    String?

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([organizationId])
  @@index([resource])
  @@index([timestamp])
  @@map("billing_usage")
}

// Quota management
model Quota {
  id             String   @id @default(cuid())
  resource       String
  limit          Float
  used           Float    @default(0)
  period         String   @default("monthly")
  resetAt        DateTime
  warningThreshold Float  @default(0.8)
  isHardLimit    Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  organizationId String

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, resource, period])
  @@index([organizationId])
  @@index([resource])
  @@map("quotas")
}

// Execution tracking
model AgentExecution {
  id          String   @id @default(cuid())
  status      String
  input       Json
  output      Json?
  error       String?
  duration    Int?
  tokens      Int?
  cost        Float?
  createdAt   DateTime @default(now())
  completedAt DateTime?
  agentId     String

  // Relations
  agent Agent @relation(fields: [agentId], references: [id], onDelete: Cascade)

  @@map("agent_executions")
}

model ToolExecution {
  id          String   @id @default(cuid())
  status      String
  input       Json
  output      Json?
  error       String?
  duration    Int?
  cost        Float?
  createdAt   DateTime @default(now())
  completedAt DateTime?
  toolId      String

  // Relations
  tool Tool @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@map("tool_executions")
}

model HybridExecution {
  id          String   @id @default(cuid())
  status      String
  input       Json
  output      Json?
  error       String?
  duration    Int?
  cost        Float?
  createdAt   DateTime @default(now())
  completedAt DateTime?
  hybridId    String

  // Relations
  hybrid Hybrid @relation(fields: [hybridId], references: [id], onDelete: Cascade)

  @@map("hybrid_executions")
}
