import { useState, useEffect, useCallback, useRef } from 'react';
import { aipx, AIPXApp, AIPXResponse } from '@/lib/aipx';
import { useToast } from '@/components/ui/use-toast';

interface UseAIPXOptions {
  appId?: string;
  appType?: string;
  autoConnect?: boolean;
  onConnected?: () => void;
  onDisconnected?: () => void;
  onError?: (error: Error) => void;
  debug?: boolean;
}

interface UseAIPXReturn {
  app: AIPXApp | null;
  connected: boolean;
  connecting: boolean;
  sendMessage: (message: string, options?: {
    sessionId?: string;
    agentId?: string;
    toolId?: string;
    hybridId?: string;
    context?: Record<string, any>;
    metadata?: Record<string, any>;
  }) => Promise<AIPXResponse>;
  connect: () => Promise<void>;
  disconnect: () => void;
  subscribeToChannels: (channels: string[]) => Promise<void>;
  unsubscribeFromChannels: (channels: string[]) => Promise<void>;
  getSessionState: (sessionId: string) => Promise<Record<string, any>>;
  updateSessionState: (sessionId: string, updates: Record<string, any>) => Promise<void>;
}

export function useAIPX(options: UseAIPXOptions = {}): UseAIPXReturn {
  const {
    appId = `app-${Math.random().toString(36).substring(2, 9)}`,
    appType = 'dashboard',
    autoConnect = true,
    onConnected,
    onDisconnected,
    onError,
    debug = false,
  } = options;

  const { toast } = useToast();
  const [app, setApp] = useState<AIPXApp | null>(null);
  const [connected, setConnected] = useState<boolean>(false);
  const [connecting, setConnecting] = useState<boolean>(false);
  const appRef = useRef<AIPXApp | null>(null);

  // Connect to AIPX server
  const connect = useCallback(async () => {
    try {
      setConnecting(true);
      
      // Get token from localStorage
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('Authentication token not found');
      }
      
      // Connect to AIPX server
      await aipx.connect(token);
      
      // Register app
      const newApp = aipx.registerApp(appId, appType);
      setApp(newApp);
      appRef.current = newApp;
      
      setConnected(true);
      setConnecting(false);
      onConnected?.();
      
      if (debug) {
        console.log(`[AIPX] App ${appId} connected`);
      }
    } catch (error) {
      setConnecting(false);
      setConnected(false);
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to connect to AIPX server';
      
      if (debug) {
        console.error(`[AIPX] Connection error:`, error);
      }
      
      toast({
        title: 'Connection Error',
        description: errorMessage,
        variant: 'destructive',
      });
      
      onError?.(error instanceof Error ? error : new Error(errorMessage));
    }
  }, [appId, appType, debug, onConnected, onError, toast]);

  // Disconnect from AIPX server
  const disconnect = useCallback(() => {
    if (appRef.current) {
      appRef.current.disconnect();
      appRef.current = null;
      setApp(null);
    }
    
    setConnected(false);
    onDisconnected?.();
    
    if (debug) {
      console.log(`[AIPX] App ${appId} disconnected`);
    }
  }, [appId, debug, onDisconnected]);

  // Send message
  const sendMessage = useCallback(async (
    message: string,
    options: {
      sessionId?: string;
      agentId?: string;
      toolId?: string;
      hybridId?: string;
      context?: Record<string, any>;
      metadata?: Record<string, any>;
    } = {}
  ): Promise<AIPXResponse> => {
    if (!appRef.current) {
      throw new Error('Not connected to AIPX server');
    }
    
    try {
      if (debug) {
        console.log(`[AIPX] Sending message:`, { message, ...options });
      }
      
      const response = await appRef.current.processAIRequest({
        message,
        sessionId: options.sessionId || '',
        agentId: options.agentId,
        toolId: options.toolId,
        hybridId: options.hybridId,
        context: options.context,
        metadata: options.metadata,
      });
      
      if (debug) {
        console.log(`[AIPX] Response received:`, response);
      }
      
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
      
      if (debug) {
        console.error(`[AIPX] Error sending message:`, error);
      }
      
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      
      onError?.(error instanceof Error ? error : new Error(errorMessage));
      throw error;
    }
  }, [debug, onError, toast]);

  // Subscribe to channels
  const subscribeToChannels = useCallback(async (channels: string[]): Promise<void> => {
    if (!appRef.current) {
      throw new Error('Not connected to AIPX server');
    }
    
    try {
      await appRef.current.subscribe(channels);
      
      if (debug) {
        console.log(`[AIPX] Subscribed to channels:`, channels);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to subscribe to channels';
      
      if (debug) {
        console.error(`[AIPX] Error subscribing to channels:`, error);
      }
      
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      
      onError?.(error instanceof Error ? error : new Error(errorMessage));
      throw error;
    }
  }, [debug, onError, toast]);

  // Unsubscribe from channels
  const unsubscribeFromChannels = useCallback(async (channels: string[]): Promise<void> => {
    if (!appRef.current) {
      throw new Error('Not connected to AIPX server');
    }
    
    try {
      await appRef.current.unsubscribe(channels);
      
      if (debug) {
        console.log(`[AIPX] Unsubscribed from channels:`, channels);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unsubscribe from channels';
      
      if (debug) {
        console.error(`[AIPX] Error unsubscribing from channels:`, error);
      }
      
      onError?.(error instanceof Error ? error : new Error(errorMessage));
      throw error;
    }
  }, [debug, onError]);

  // Get session state
  const getSessionState = useCallback(async (sessionId: string): Promise<Record<string, any>> => {
    if (!appRef.current) {
      throw new Error('Not connected to AIPX server');
    }
    
    try {
      const state = await appRef.current.getSessionState(sessionId);
      
      if (debug) {
        console.log(`[AIPX] Got session state for ${sessionId}:`, state);
      }
      
      return state;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get session state';
      
      if (debug) {
        console.error(`[AIPX] Error getting session state:`, error);
      }
      
      onError?.(error instanceof Error ? error : new Error(errorMessage));
      throw error;
    }
  }, [debug, onError]);

  // Update session state
  const updateSessionState = useCallback(async (
    sessionId: string,
    updates: Record<string, any>
  ): Promise<void> => {
    if (!appRef.current) {
      throw new Error('Not connected to AIPX server');
    }
    
    try {
      await appRef.current.updateSessionState(sessionId, updates);
      
      if (debug) {
        console.log(`[AIPX] Updated session state for ${sessionId}:`, updates);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update session state';
      
      if (debug) {
        console.error(`[AIPX] Error updating session state:`, error);
      }
      
      onError?.(error instanceof Error ? error : new Error(errorMessage));
      throw error;
    }
  }, [debug, onError]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }
    
    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  // Listen for connection changes
  useEffect(() => {
    const unsubscribe = aipx.onConnectionChange((isConnected) => {
      setConnected(isConnected);
      
      if (isConnected) {
        onConnected?.();
      } else {
        onDisconnected?.();
      }
    });
    
    return unsubscribe;
  }, [onConnected, onDisconnected]);

  return {
    app,
    connected,
    connecting,
    sendMessage,
    connect,
    disconnect,
    subscribeToChannels,
    unsubscribeFromChannels,
    getSessionState,
    updateSessionState,
  };
}