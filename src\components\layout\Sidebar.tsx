"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Icons } from "@/components/ui/icons";
import { Badge } from "@/components/ui/badge";
import { usePathname } from "next/navigation";
import Link from "next/link";
import {
  ChevronLeft,
  ChevronRight,
  LayoutDashboard,
  Bot,
  Wrench,
  GitBranch,
  BookOpen,
  BarChart2,
  Settings,
  Bell,
} from "lucide-react";

interface SidebarProps {
  className?: string;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

export default function Sidebar({
  className,
  collapsed = false,
  onToggleCollapse,
}: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(collapsed);
  const pathname = usePathname();

  const handleToggleCollapse = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    if (onToggleCollapse) {
      onToggleCollapse();
    }
  };

  const navItems = [
    {
      title: "Dashboard",
      icon: <LayoutDashboard className="h-5 w-5" />,
      href: "/dashboard",
      notifications: 0,
    },
    {
      title: "Agents",
      icon: <Bot className="h-5 w-5" />,
      href: "/dashboard/agents",
      notifications: 3,
    },
    {
      title: "Tools",
      icon: <Wrench className="h-5 w-5" />,
      href: "/dashboard/tools",
      notifications: 0,
    },
    {
      title: "Workflows",
      icon: <GitBranch className="h-5 w-5" />,
      href: "/dashboard/workflows",
      notifications: 2,
    },
    {
      title: "Knowledge Base",
      icon: <BookOpen className="h-5 w-5" />,
      href: "/dashboard/knowledge",
      notifications: 0,
    },
    {
      title: "Analytics",
      icon: <BarChart2 className="h-5 w-5" />,
      href: "/dashboard/analytics",
      notifications: 0,
    },
    {
      title: "Settings",
      icon: <Settings className="h-5 w-5" />,
      href: "/dashboard/settings",
      notifications: 1,
    },
  ];

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-background border-r transition-all duration-300",
        isCollapsed ? "w-16" : "w-64",
        className,
      )}
    >
      <div className="flex items-center justify-between p-4 border-b">
        {!isCollapsed && (
          <div className="flex items-center gap-2">
            <Icons.logo className="h-6 w-6" />
            <span className="font-bold text-lg">SynapseAI</span>
          </div>
        )}
        {isCollapsed && <Icons.logo className="h-6 w-6 mx-auto" />}
        <Button
          variant="ghost"
          size="icon"
          className={cn("ml-auto", isCollapsed && "mx-auto")}
          onClick={handleToggleCollapse}
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-1 px-2">
          {navItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <TooltipProvider key={item.href} delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center gap-3 px-3 py-2 rounded-md group transition-colors",
                        isActive
                          ? "bg-primary/10 text-primary hover:bg-primary/20"
                          : "text-muted-foreground hover:bg-muted hover:text-foreground",
                      )}
                    >
                      {item.icon}
                      {!isCollapsed && (
                        <span className="flex-1 truncate">{item.title}</span>
                      )}
                      {!isCollapsed && item.notifications > 0 && (
                        <Badge variant="secondary" className="ml-auto">
                          {item.notifications}
                        </Badge>
                      )}
                      {isCollapsed && item.notifications > 0 && (
                        <Badge
                          variant="secondary"
                          className="absolute top-0 right-0 translate-x-1/3 -translate-y-1/3 h-5 w-5 flex items-center justify-center p-0"
                        >
                          {item.notifications}
                        </Badge>
                      )}
                    </Link>
                  </TooltipTrigger>
                  {isCollapsed && (
                    <TooltipContent side="right">{item.title}</TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </nav>
      </div>

      <div className="mt-auto border-t p-4">
        <div className="flex items-center gap-3">
          <Avatar className="h-9 w-9">
            <AvatarImage
              src="https://api.dicebear.com/7.x/avataaars/svg?seed=user123"
              alt="User"
            />
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
          {!isCollapsed && (
            <div className="flex-1 truncate">
              <p className="text-sm font-medium">John Doe</p>
              <p className="text-xs text-muted-foreground truncate">
                <EMAIL>
              </p>
            </div>
          )}
          {!isCollapsed && (
            <Button variant="ghost" size="icon" className="ml-auto">
              <Bell className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
