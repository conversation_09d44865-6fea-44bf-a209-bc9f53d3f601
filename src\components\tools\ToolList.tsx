"use client";

import { useState, useEffect } from "react";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Play,
  Copy,
  Edit,
  Trash2,
} from "lucide-react";
import { Tool, ToolStatus } from "../../../shared/types";
import { apiClient } from "@/lib/api";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON>alog,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/components/ui/use-toast";
import { ToolForm } from "./ToolForm";
import { ToolDetails } from "./ToolDetails";
import { ToolExecuteDialog } from "./ToolExecuteDialog";

interface ToolListProps {
  onToolSelect?: (tool: Tool) => void;
}

export function ToolList({ onToolSelect }: ToolListProps) {
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<ToolStatus | "all">("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showExecuteDialog, setShowExecuteDialog] = useState(false);
  const [editingTool, setEditingTool] = useState<Tool | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const { toast } = useToast();

  const fetchTools = async () => {
    try {
      setLoading(true);
      const response = await apiClient.getTools({
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm || undefined,
        status: statusFilter !== "all" ? statusFilter : undefined,
        category: categoryFilter !== "all" ? categoryFilter : undefined,
        sortBy: "updatedAt",
        sortOrder: "desc",
      });

      if (response.success && response.data) {
        setTools(response.data);
        if (response.pagination) {
          setPagination(response.pagination);
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch tools",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTools();
  }, [pagination.page, searchTerm, statusFilter, categoryFilter]);

  const handleCreateTool = async (toolData: any) => {
    try {
      const response = await apiClient.createTool(toolData);
      if (response.success) {
        toast({
          title: "Success",
          description: "Tool created successfully",
        });
        setShowCreateDialog(false);
        fetchTools();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create tool",
        variant: "destructive",
      });
    }
  };

  const handleUpdateTool = async (toolData: any) => {
    if (!editingTool) return;

    try {
      const response = await apiClient.updateTool(editingTool.id, toolData);
      if (response.success) {
        toast({
          title: "Success",
          description: "Tool updated successfully",
        });
        setEditingTool(null);
        fetchTools();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update tool",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTool = async (toolId: string) => {
    try {
      await apiClient.deleteTool(toolId);
      toast({
        title: "Success",
        description: "Tool deleted successfully",
      });
      fetchTools();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete tool",
        variant: "destructive",
      });
    }
  };

  const handleDuplicateTool = async (toolId: string) => {
    try {
      const response = await apiClient.duplicateTool(toolId);
      if (response.success) {
        toast({
          title: "Success",
          description: "Tool duplicated successfully",
        });
        fetchTools();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate tool",
        variant: "destructive",
      });
    }
  };

  const handleToolClick = (tool: Tool) => {
    setSelectedTool(tool);
    if (onToolSelect) {
      onToolSelect(tool);
    } else {
      setShowDetailsDialog(true);
    }
  };

  const getStatusBadge = (status: ToolStatus) => {
    const variants = {
      [ToolStatus.DRAFT]: "secondary",
      [ToolStatus.ACTIVE]: "default",
      [ToolStatus.ARCHIVED]: "outline",
    } as const;

    return (
      <Badge variant={variants[status] || "secondary"}>
        {status.toLowerCase()}
      </Badge>
    );
  };

  const categories = Array.from(
    new Set(tools.map((tool) => tool.category).filter(Boolean)),
  );

  return (
    <div className="bg-white min-h-screen p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tools</h1>
            <p className="text-gray-600 mt-1">
              Create and manage API tools for your workflows
            </p>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Tool
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Tool</DialogTitle>
              </DialogHeader>
              <ToolForm
                onSubmit={handleCreateTool}
                onCancel={() => setShowCreateDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search tools..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select
                value={statusFilter}
                onValueChange={(value) =>
                  setStatusFilter(value as ToolStatus | "all")
                }
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value={ToolStatus.DRAFT}>Draft</SelectItem>
                  <SelectItem value={ToolStatus.ACTIVE}>Active</SelectItem>
                  <SelectItem value={ToolStatus.ARCHIVED}>Archived</SelectItem>
                </SelectContent>
              </Select>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category!}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Tools Table */}
        <Card>
          <CardHeader>
            <CardTitle>Tools ({pagination.total})</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : tools.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No tools found</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Executions</TableHead>
                    <TableHead>Updated</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tools.map((tool) => (
                    <TableRow
                      key={tool.id}
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleToolClick(tool)}
                    >
                      <TableCell>
                        <div>
                          <div className="font-medium">{tool.name}</div>
                          {tool.description && (
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {tool.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {tool.category && (
                          <Badge variant="outline">{tool.category}</Badge>
                        )}
                      </TableCell>
                      <TableCell>{getStatusBadge(tool.status)}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">{tool.method}</Badge>
                      </TableCell>
                      <TableCell>{tool._count?.executions || 0}</TableCell>
                      <TableCell>
                        {new Date(tool.updatedAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedTool(tool);
                                setShowExecuteDialog(true);
                              }}
                            >
                              <Play className="h-4 w-4 mr-2" />
                              Execute
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => setEditingTool(tool)}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDuplicateTool(tool.id)}
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Duplicate
                            </DropdownMenuItem>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem
                                  onSelect={(e) => e.preventDefault()}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Delete Tool
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete &quot;
                                    {tool.name}&quot;? This action cannot be
                                    undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteTool(tool.id)}
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
              of {pagination.total} results
            </p>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
                }
                disabled={pagination.page <= 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
                }
                disabled={pagination.page >= pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Tool Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Tool Details</DialogTitle>
          </DialogHeader>
          {selectedTool && (
            <ToolDetails
              tool={selectedTool}
              onClose={() => setShowDetailsDialog(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Tool Dialog */}
      <Dialog
        open={!!editingTool}
        onOpenChange={(open) => !open && setEditingTool(null)}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Tool</DialogTitle>
          </DialogHeader>
          {editingTool && (
            <ToolForm
              tool={editingTool}
              onSubmit={handleUpdateTool}
              onCancel={() => setEditingTool(null)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Execute Tool Dialog */}
      <Dialog open={showExecuteDialog} onOpenChange={setShowExecuteDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Execute Tool</DialogTitle>
          </DialogHeader>
          {selectedTool && (
            <ToolExecuteDialog
              tool={selectedTool}
              onClose={() => setShowExecuteDialog(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
