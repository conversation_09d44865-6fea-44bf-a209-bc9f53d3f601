import { Injectable, Logger } from "@nestjs/common";
import { ApixService } from "../apix/apix.service";
import { PrismaService } from "../prisma/prisma.service";
import { AnalyticsService } from "../analytics/analytics.service";

export interface RouteCommand {
  type: string;
  payload: Record<string, any>;
  target?: {
    appId?: string;
    userId?: string;
    sessionId?: string;
    organizationId: string;
  };
}

@Injectable()
export class RouterEngine {
  private readonly logger = new Logger(RouterEngine.name);

  constructor(
    private apixService: ApixService,
    private prisma: PrismaService,
    private analyticsService: AnalyticsService
  ) {}

  async route(command: RouteCommand): Promise<void> {
    try {
      this.logger.log(`Routing command: ${command.type}`);

      // Track analytics
      await this.analyticsService.track("router_command", {
        commandType: command.type,
        hasTarget: !!command.target,
        organizationId: command.target?.organizationId,
        userId: command.target?.userId,
      });

      // Process command based on type
      switch (command.type) {
        case "navigate_to":
          await this.handleNavigationCommand(command);
          break;
        case "inject_dom_instruction":
          await this.handleDOMInjectionCommand(command);
          break;
        case "show_notification":
          await this.handleNotificationCommand(command);
          break;
        case "update_ui_state":
          await this.handleUIStateCommand(command);
          break;
        case "execute_action":
          await this.handleActionCommand(command);
          break;
        default:
          // For unknown commands, just pass through as a control signal
          await this.emitControlSignal(command);
      }
    } catch (error) {
      this.logger.error(`Error routing command: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  private async handleNavigationCommand(command: RouteCommand): Promise<void> {
    const { route, params, options } = command.payload;

    await this.apixService.emit({
      type: "control_signal",
      payload: {
        action: "navigate",
        route,
        params,
        options,
      },
      organizationId: command.target?.organizationId || "",
      userId: command.target?.userId || "",
      sessionId: command.target?.sessionId || "",
    });
  }

  private async handleDOMInjectionCommand(command: RouteCommand): Promise<void> {
    const { selector, content, operation, options } = command.payload;

    await this.apixService.emit({
      type: "control_signal",
      payload: {
        action: "dom_update",
        selector,
        content,
        operation: operation || "replace",
        options: options || {},
      },
      organizationId: command.target?.organizationId || "",
      userId: command.target?.userId || "",
      sessionId: command.target?.sessionId || "",
    });
  }

  private async handleNotificationCommand(command: RouteCommand): Promise<void> {
    const { title, message, type, duration } = command.payload;

    await this.apixService.emit({
      type: "notification",
      payload: {
        title,
        message,
        type: type || "info",
        duration: duration || 5000,
      },
      organizationId: command.target?.organizationId || "",
      userId: command.target?.userId || "",
      sessionId: command.target?.sessionId || "",
    });
  }

  private async handleUIStateCommand(command: RouteCommand): Promise<void> {
    const { stateUpdates } = command.payload;

    await this.apixService.emit({
      type: "ui_state_update",
      payload: {
        updates: stateUpdates,
      },
      organizationId: command.target?.organizationId || "",
      userId: command.target?.userId || "",
      sessionId: command.target?.sessionId || "",
    });
  }

  private async handleActionCommand(command: RouteCommand): Promise<void> {
    const { action, params } = command.payload;

    await this.apixService.emit({
      type: "action",
      payload: {
        action,
        params,
      },
      organizationId: command.target?.organizationId || "",
      userId: command.target?.userId || "",
      sessionId: command.target?.sessionId || "",
    });
  }

  private async emitControlSignal(command: RouteCommand): Promise<void> {
    await this.apixService.emit({
      type: "control_signal",
      payload: {
        action: command.type,
        ...command.payload,
      },
        organizationId: command.target?.organizationId || "",
      userId: command.target?.userId || "",
      sessionId: command.target?.sessionId || "",
    });
  }
}