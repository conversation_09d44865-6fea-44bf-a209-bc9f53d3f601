"use client";

import React, { useState } from "react";
import { Tool } from "../../../../shared/types";
import Sidebar from "@/components/layout/Sidebar";
import { ToolList } from "@/components/tools/ToolList";
import { ToolForm } from "@/components/tools/ToolForm";
import { ToolDetails } from "@/components/tools/ToolDetails";
import { ToolExecuteDialog } from "@/components/tools/ToolExecuteDialog";
import { Toaster } from "@/components/ui/toaster";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

type ViewMode = "list" | "create" | "edit" | "details";

export default function ToolsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [executeDialogOpen, setExecuteDialogOpen] = useState(false);
  const [executeTool, setExecuteTool] = useState<Tool | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  const handleCreateTool = () => {
    setSelectedTool(null);
    setCreateDialogOpen(true);
  };

  const handleEditTool = (tool: Tool) => {
    setSelectedTool(tool);
    setEditDialogOpen(true);
  };

  const handleViewTool = (tool: Tool) => {
    setSelectedTool(tool);
    setDetailsDialogOpen(true);
  };

  const handleExecuteTool = (tool: Tool) => {
    setExecuteTool(tool);
    setExecuteDialogOpen(true);
  };

  const handleSaveTool = () => {
    setCreateDialogOpen(false);
    setEditDialogOpen(false);
    setSelectedTool(null);
  };

  const handleCancelForm = () => {
    setCreateDialogOpen(false);
    setEditDialogOpen(false);
    setSelectedTool(null);
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar Navigation */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="container mx-auto p-6">
          {/* Breadcrumb */}
          <div className="mb-6">
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="inline-flex items-center space-x-1 md:space-x-3">
                <li className="inline-flex items-center">
                  <span className="inline-flex items-center text-sm font-medium text-gray-700">
                    Tools
                  </span>
                </li>
              </ol>
            </nav>
          </div>

          {/* Page Content */}
          <ToolList
            onToolSelect={handleViewTool}
          />
        </div>
      </div>

      {/* Create Tool Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Tool</DialogTitle>
          </DialogHeader>
          <ToolForm
            onSubmit={handleSaveTool}
            onCancel={handleCancelForm}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Tool Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Tool</DialogTitle>
          </DialogHeader>
          {selectedTool && (
            <ToolForm
              tool={selectedTool}
              onSubmit={handleSaveTool}
              onCancel={handleCancelForm}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Tool Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Tool Details</DialogTitle>
          </DialogHeader>
          {selectedTool && (
            <ToolDetails
              tool={selectedTool}
              onClose={() => setDetailsDialogOpen(false)}
              onEdit={handleEditTool}
              onExecute={handleExecuteTool}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Execute Tool Dialog */}
      <Dialog open={executeDialogOpen} onOpenChange={setExecuteDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Execute Tool</DialogTitle>
          </DialogHeader>
          {executeTool && (
            <ToolExecuteDialog
              tool={executeTool}
              onClose={() => setExecuteDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
}
