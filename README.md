# SynapseAI Platform

A production-ready SaaS platform for creating AI agents, building tools, managing hybrid workflows, handling approvals, organizing knowledge, generating widgets, and comprehensive monitoring - all through one unified, multi-tenant system.

## 🏗️ Architecture

- **Frontend**: Next.js 14 (App Router) with React 18
- **Backend**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Cache/Sessions**: Redis
- **Real-time**: WebSocket (APIX protocol)
- **Message Queue**: Bull/BullMQ
- **Monitoring**: Prometheus + Grafana
- **Error Tracking**: Sentry
- **Logging**: Winston
- **Load Balancer**: NGINX

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Docker & Docker Compose
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd synapseai-platform
   ```

2. **Install frontend dependencies**
   ```bash
   npm install
   ```

3. **Setup backend**
   ```bash
   npm run setup:backend
   ```

4. **Start development environment**
   ```bash
   npm run docker:dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001
   - Grafana: http://localhost:3002 (admin/admin)
   - Prometheus: http://localhost:9090

## 📁 Project Structure

```
synapseai/
├── frontend/                 # Next.js frontend application
│   ├── src/app/             # App Router structure
│   ├── src/components/      # Reusable UI components
│   └── src/lib/             # Frontend utilities
├── backend/                 # NestJS backend service
│   ├── src/                 # Source code
│   ├── prisma/              # Database schema and migrations
│   └── test/                # Test files
├── shared/                  # Shared TypeScript types
├── nginx/                   # NGINX configuration
├── monitoring/              # Prometheus & Grafana config
├── .github/workflows/       # CI/CD pipelines
└── docker-compose.yml       # Development environment
```

## 🛠️ Development Commands

### Frontend
```bash
npm run dev                  # Start Next.js dev server
npm run build                # Build for production
npm run lint                 # Run ESLint
npm run type-check           # TypeScript validation
```

### Backend
```bash
cd backend
npm run start:dev            # Start NestJS dev server
npm run build                # Build for production
npm run test                 # Run unit tests
npm run test:e2e             # Run e2e tests
npm run db:migrate           # Run database migrations
npm run db:studio            # Open Prisma Studio
```

### Docker
```bash
npm run docker:dev           # Start all services
npm run docker:build         # Build all images
npm run docker:down          # Stop all services
```

## 🏭 Production Deployment

### Environment Variables

Create `.env.production` with:

```env
# Database
DATABASE_URL=************************************/synapseai

# Redis
REDIS_URL=redis://host:6379

# JWT
JWT_SECRET=your-super-secure-jwt-secret

# Sentry
SENTRY_DSN=your-sentry-dsn

# External Services
OPENAI_API_KEY=your-openai-key
CLAUDE_API_KEY=your-claude-key
# ... other AI provider keys
```

### Docker Production Build

```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Kubernetes Deployment

```bash
kubectl apply -f k8s/
```

## 📊 Monitoring & Observability

### Metrics (Prometheus)
- Application performance metrics
- Database connection pools
- Redis cache hit rates
- Custom business metrics

### Dashboards (Grafana)
- System overview
- Application performance
- Database metrics
- User activity

### Error Tracking (Sentry)
- Real-time error monitoring
- Performance monitoring
- Release tracking

### Logging (Winston)
- Structured JSON logging
- Log levels and filtering
- Centralized log aggregation

## 🔒 Security Features

- JWT-based authentication
- Role-based access control (RBAC)
- Rate limiting
- CORS protection
- Security headers (Helmet)
- Input validation
- SQL injection prevention
- XSS protection

## 🧪 Testing

### Backend Testing
```bash
cd backend
npm run test                 # Unit tests
npm run test:e2e             # End-to-end tests
npm run test:cov             # Coverage report
```

### Frontend Testing
```bash
npm run test                 # Jest tests
npm run test:e2e             # Playwright e2e tests
```

## 📚 API Documentation

API documentation is available at:
- Development: http://localhost:3001/api/docs
- Production: https://api.yourdomain.com/docs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, please:
1. Check the documentation
2. Search existing issues
3. Create a new issue with detailed information

## 🗺️ Roadmap

- [ ] Advanced AI provider integrations
- [ ] Enhanced workflow automation
- [ ] Mobile application
- [ ] Advanced analytics and reporting
- [ ] Enterprise SSO integrations
- [ ] Multi-region deployment
