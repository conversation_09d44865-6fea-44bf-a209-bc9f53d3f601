import React from "react";
import DashboardOverview from "@/components/dashboard/DashboardOverview";
import ActivityFeed from "@/components/dashboard/ActivityFeed";
import ResourceManagement from "@/components/dashboard/ResourceManagement";
import Sidebar from "@/components/layout/Sidebar";

export default function DashboardPage() {
  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar Navigation */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Page Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
              <p className="text-muted-foreground mt-1">
                Welcome to SynapseAI Platform
              </p>
            </div>
            <div className="flex space-x-2">
              <button className="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md text-sm font-medium">
                Create Agent
              </button>
              <button className="bg-secondary text-secondary-foreground hover:bg-secondary/90 px-4 py-2 rounded-md text-sm font-medium">
                Create Tool
              </button>
            </div>
          </div>

          {/* Dashboard Overview */}
          <DashboardOverview />

          {/* Main Dashboard Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Activity Feed (1/3 width on large screens) */}
            <div className="lg:col-span-1">
              <ActivityFeed />
            </div>

            {/* Resource Management (2/3 width on large screens) */}
            <div className="lg:col-span-2">
              <ResourceManagement />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
