import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  ConflictException,
  Logger,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { ProvidersService } from "../providers/providers.service";
import { BillingService } from "../billing/billing.service";
import { AnalyticsService } from "../analytics/analytics.service";
import { ApixService } from "../apix/apix.service";
import { CreateAgentDto } from "./dto/create-agent.dto";
import { UpdateAgentDto } from "./dto/update-agent.dto";
import { QueryAgentsDto } from "./dto/query-agents.dto";
import { ExecuteAgentDto } from "./dto/execute-agent.dto";
import { Agent, AgentStatus, UserRole } from "@prisma/client";
import { AuthenticatedRequest } from "../common/middleware/tenant.middleware";

@Injectable()
export class AgentsService {
  private readonly logger = new Logger(AgentsService.name);

  constructor(
    private prisma: PrismaService,
    private providersService: ProvidersService,
    private billingService: BillingService,
    private analyticsService: AnalyticsService,
    private apixService: ApixService,
  ) {}

  async create(
    createAgentDto: CreateAgentDto,
    user: AuthenticatedRequest["user"],
  ): Promise<Agent> {
    try {
      // Check if template exists and user has access
      if (createAgentDto.templateId) {
        const template = await this.prisma.template.findFirst({
          where: {
            id: createAgentDto.templateId,
            OR: [{ organizationId: user.organizationId }, { isPublic: true }],
          },
        });

        if (!template) {
          throw new NotFoundException("Template not found or access denied");
        }
      }

      const agent = await this.prisma.agent.create({
        data: {
          ...createAgentDto,
          userId: user.id,
          organizationId: user.organizationId,
          version: 1,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          template: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          _count: {
            select: {
              executions: true,
              sessions: true,
            },
          },
        },
      });

      this.logger.log(
        `Agent ${agent.name} created by user ${user.email} in organization ${user.organizationId}`,
      );

      return agent;
    } catch (error) {
      this.logger.error("Failed to create agent:", error);
      throw error;
    }
  }

  async findAll(queryDto: QueryAgentsDto, user: AuthenticatedRequest["user"]) {
    const { page, limit, search, status, isPublic, tags, sortBy, sortOrder } =
      queryDto;

    const skip = (page - 1) * limit;

    const where: any = {
      OR: [{ organizationId: user.organizationId }, { isPublic: true }],
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (typeof isPublic === "boolean") {
      where.isPublic = isPublic;
    }

    if (tags && tags.length > 0) {
      where.tags = {
        hasSome: tags,
      };
    }

    const [agents, total] = await Promise.all([
      this.prisma.agent.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          template: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          _count: {
            select: {
              executions: true,
              sessions: true,
            },
          },
        },
      }),
      this.prisma.agent.count({ where }),
    ]);

    return {
      data: agents,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(
    id: string,
    user: AuthenticatedRequest["user"],
  ): Promise<Agent> {
    const agent = await this.prisma.agent.findFirst({
      where: {
        id,
        OR: [{ organizationId: user.organizationId }, { isPublic: true }],
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        template: {
          select: {
            id: true,
            name: true,
            type: true,
            content: true,
            variables: true,
          },
        },
        executions: {
          take: 10,
          orderBy: {
            createdAt: "desc",
          },
          select: {
            id: true,
            status: true,
            createdAt: true,
            completedAt: true,
            duration: true,
            tokens: true,
            cost: true,
          },
        },
        _count: {
          select: {
            executions: true,
            sessions: true,
            hybrids: true,
            widgets: true,
          },
        },
      },
    });

    if (!agent) {
      throw new NotFoundException("Agent not found");
    }

    return agent;
  }

  async update(
    id: string,
    updateAgentDto: UpdateAgentDto,
    user: AuthenticatedRequest["user"],
  ): Promise<Agent> {
    const existingAgent = await this.prisma.agent.findFirst({
      where: {
        id,
        organizationId: user.organizationId,
      },
    });

    if (!existingAgent) {
      throw new NotFoundException("Agent not found");
    }

    // Check permissions
    if (
      existingAgent.userId !== user.id &&
      user.role !== UserRole.ORG_ADMIN &&
      user.role !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException(
        "You don't have permission to update this agent",
      );
    }

    // Check if template exists and user has access
    if (updateAgentDto.templateId) {
      const template = await this.prisma.template.findFirst({
        where: {
          id: updateAgentDto.templateId,
          OR: [{ organizationId: user.organizationId }, { isPublic: true }],
        },
      });

      if (!template) {
        throw new NotFoundException("Template not found or access denied");
      }
    }

    try {
      const updatedAgent = await this.prisma.agent.update({
        where: { id },
        data: {
          ...updateAgentDto,
          updatedAt: new Date(),
          version: updateAgentDto.version || existingAgent.version + 1,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          template: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          _count: {
            select: {
              executions: true,
              sessions: true,
            },
          },
        },
      });

      this.logger.log(
        `Agent ${updatedAgent.name} updated by user ${user.email}`,
      );

      return updatedAgent;
    } catch (error) {
      this.logger.error("Failed to update agent:", error);
      throw error;
    }
  }

  async remove(id: string, user: AuthenticatedRequest["user"]): Promise<void> {
    const existingAgent = await this.prisma.agent.findFirst({
      where: {
        id,
        organizationId: user.organizationId,
      },
    });

    if (!existingAgent) {
      throw new NotFoundException("Agent not found");
    }

    // Check permissions
    if (
      existingAgent.userId !== user.id &&
      user.role !== UserRole.ORG_ADMIN &&
      user.role !== UserRole.SUPER_ADMIN
    ) {
      throw new ForbiddenException(
        "You don't have permission to delete this agent",
      );
    }

    // Check if agent is being used
    const usage = await this.prisma.agent.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            sessions: { where: { status: "ACTIVE" } },
            hybrids: true,
            widgets: { where: { status: "ACTIVE" } },
          },
        },
      },
    });

    if (
      usage._count.sessions > 0 ||
      usage._count.hybrids > 0 ||
      usage._count.widgets > 0
    ) {
      throw new ConflictException(
        "Cannot delete agent that is currently in use. Archive it instead.",
      );
    }

    try {
      await this.prisma.agent.delete({
        where: { id },
      });

      this.logger.log(
        `Agent ${existingAgent.name} deleted by user ${user.email}`,
      );
    } catch (error) {
      this.logger.error("Failed to delete agent:", error);
      throw error;
    }
  }

  async execute(
    id: string,
    executeDto: ExecuteAgentDto,
    user: AuthenticatedRequest["user"],
  ) {
    const agent = await this.findOne(id, user);

    if (agent.status !== AgentStatus.ACTIVE) {
      throw new ConflictException("Agent is not active");
    }

    // Check quota
    await this.billingService.checkQuota(
      user.organizationId,
      "agent_executions",
      1,
    );

    const startTime = Date.now();
    let execution;

    try {
      // Create execution record
      execution = await this.prisma.agentExecution.create({
        data: {
          agentId: id,
          status: "RUNNING",
          input: executeDto,
        },
      });

      // Emit real-time event
      await this.apixService.emitAgentEvent(
        "agent_execution_started",
        id,
        user.organizationId,
        user.id,
        { executionId: execution.id, input: executeDto },
      );

      // Select optimal provider
      const provider = await this.providersService.selectOptimalProvider(
        agent.model,
        "text_generation",
        user.organizationId,
        { temperature: agent.temperature, maxTokens: agent.maxTokens },
      );

      // Execute with provider
      const result = await this.executeWithProvider(
        agent,
        executeDto,
        provider,
        execution.id,
      );

      const duration = Date.now() - startTime;
      const cost = this.calculateExecutionCost(
        result.tokens || 0,
        provider.costPerToken || 0.001,
      );

      // Update execution record
      await this.prisma.agentExecution.update({
        where: { id: execution.id },
        data: {
          status: "COMPLETED",
          output: result.response,
          duration,
          tokens: result.tokens,
          cost,
          completedAt: new Date(),
        },
      });

      // Track usage and billing
      await this.billingService.trackUsage(
        user.organizationId,
        "agent_executions",
        1,
        cost,
        {
          agentId: id,
          executionId: execution.id,
          tokens: result.tokens,
          model: agent.model,
          providerId: provider.id,
        },
      );

      // Track analytics
      await this.analyticsService.track("agent_executed", {
        agentId: id,
        executionId: execution.id,
        organizationId: user.organizationId,
        userId: user.id,
        duration,
        tokens: result.tokens,
        cost,
        model: agent.model,
        providerId: provider.id,
      });

      // Emit completion event
      await this.apixService.emitAgentEvent(
        "agent_execution_completed",
        id,
        user.organizationId,
        user.id,
        {
          executionId: execution.id,
          duration,
          tokens: result.tokens,
          cost,
          success: true,
        },
      );

      return {
        executionId: execution.id,
        status: "COMPLETED",
        response: result.response,
        tokens: result.tokens,
        cost,
        duration,
        provider: provider.name,
      };
    } catch (error) {
      this.logger.error("Failed to execute agent:", error);

      if (execution) {
        // Update execution with error
        await this.prisma.agentExecution.update({
          where: { id: execution.id },
          data: {
            status: "FAILED",
            error: error.message,
            completedAt: new Date(),
            duration: Date.now() - startTime,
          },
        });

        // Emit failure event
        await this.apixService.emitAgentEvent(
          "agent_execution_failed",
          id,
          user.organizationId,
          user.id,
          {
            executionId: execution.id,
            error: error.message,
            duration: Date.now() - startTime,
          },
        );
      }

      throw error;
    }
  }

  private async executeWithProvider(
    agent: any,
    executeDto: ExecuteAgentDto,
    provider: any,
    executionId: string,
  ): Promise<{ response: any; tokens: number }> {
    const { input, temperature, maxTokens, context } = executeDto;

    // Build the prompt
    const systemPrompt =
      agent.systemPrompt || "You are a helpful AI assistant.";
    const userPrompt = agent.prompt.replace(/{{input}}/g, input);

    // Prepare messages
    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt },
    ];

    // Add context if provided
    if (context && Object.keys(context).length > 0) {
      messages.splice(1, 0, {
        role: "system",
        content: `Context: ${JSON.stringify(context)}`,
      });
    }

    // Execute based on provider type
    switch (provider.type) {
      case "OPENAI":
        return await this.executeOpenAI(provider, messages, {
          temperature: temperature || agent.temperature,
          maxTokens: maxTokens || agent.maxTokens,
          model: agent.model,
        });

      case "CLAUDE":
        return await this.executeAnthropic(provider, messages, {
          temperature: temperature || agent.temperature,
          maxTokens: maxTokens || agent.maxTokens,
          model: agent.model,
        });

      case "GEMINI":
        return await this.executeGoogle(provider, messages, {
          temperature: temperature || agent.temperature,
          maxTokens: maxTokens || agent.maxTokens,
          model: agent.model,
        });

      default:
        throw new Error(`Unsupported provider type: ${provider.type}`);
    }
  }

  private async executeOpenAI(
    provider: any,
    messages: any[],
    options: any,
  ): Promise<{ response: any; tokens: number }> {
    const { configuration } = provider;

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${configuration.apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: options.model,
        messages,
        temperature: options.temperature,
        max_tokens: options.maxTokens,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `OpenAI API error: ${error.error?.message || response.statusText}`,
      );
    }

    const data = await response.json();

    return {
      response: {
        content: data.choices[0]?.message?.content || "",
        finishReason: data.choices[0]?.finish_reason,
        model: data.model,
      },
      tokens: data.usage?.total_tokens || 0,
    };
  }

  private async executeAnthropic(
    provider: any,
    messages: any[],
    options: any,
  ): Promise<{ response: any; tokens: number }> {
    const { configuration } = provider;

    // Convert messages format for Anthropic
    const systemMessage =
      messages.find((m) => m.role === "system")?.content || "";
    const userMessages = messages.filter((m) => m.role !== "system");

    const response = await fetch("https://api.anthropic.com/v1/messages", {
      method: "POST",
      headers: {
        "x-api-key": configuration.apiKey,
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01",
      },
      body: JSON.stringify({
        model: options.model,
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        system: systemMessage,
        messages: userMessages,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `Anthropic API error: ${error.error?.message || response.statusText}`,
      );
    }

    const data = await response.json();

    return {
      response: {
        content: data.content[0]?.text || "",
        stopReason: data.stop_reason,
        model: data.model,
      },
      tokens: data.usage?.input_tokens + data.usage?.output_tokens || 0,
    };
  }

  private async executeGoogle(
    provider: any,
    messages: any[],
    options: any,
  ): Promise<{ response: any; tokens: number }> {
    const { configuration } = provider;

    // Convert messages format for Google
    const contents = messages.map((msg) => ({
      role: msg.role === "assistant" ? "model" : "user",
      parts: [{ text: msg.content }],
    }));

    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/${options.model}:generateContent?key=${configuration.apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contents,
          generationConfig: {
            temperature: options.temperature,
            maxOutputTokens: options.maxTokens,
          },
        }),
      },
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `Google API error: ${error.error?.message || response.statusText}`,
      );
    }

    const data = await response.json();

    return {
      response: {
        content: data.candidates[0]?.content?.parts[0]?.text || "",
        finishReason: data.candidates[0]?.finishReason,
        model: options.model,
      },
      tokens: data.usageMetadata?.totalTokenCount || 0,
    };
  }

  private calculateExecutionCost(tokens: number, costPerToken: number): number {
    return tokens * costPerToken;
  }

  async getExecutions(
    id: string,
    user: AuthenticatedRequest["user"],
    page: number = 1,
    limit: number = 10,
  ) {
    // Verify agent access
    await this.findOne(id, user);

    const skip = (page - 1) * limit;

    const [executions, total] = await Promise.all([
      this.prisma.agentExecution.findMany({
        where: { agentId: id },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      this.prisma.agentExecution.count({
        where: { agentId: id },
      }),
    ]);

    return {
      data: executions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async duplicate(
    id: string,
    user: AuthenticatedRequest["user"],
  ): Promise<Agent> {
    const originalAgent = await this.findOne(id, user);

    const duplicateData = {
      name: `${originalAgent.name} (Copy)`,
      description: originalAgent.description,
      prompt: originalAgent.prompt,
      model: originalAgent.model,
      temperature: originalAgent.temperature,
      maxTokens: originalAgent.maxTokens,
      systemPrompt: originalAgent.systemPrompt,
      status: AgentStatus.DRAFT,
      isPublic: false,
      tags: originalAgent.tags,
      metadata: originalAgent.metadata,
      templateId: originalAgent.templateId,
    };

    return this.create(duplicateData, user);
  }

  async getStats(user: AuthenticatedRequest["user"]) {
    const stats = await this.prisma.agent.groupBy({
      by: ["status"],
      where: {
        organizationId: user.organizationId,
      },
      _count: {
        id: true,
      },
    });

    const totalExecutions = await this.prisma.agentExecution.count({
      where: {
        agent: {
          organizationId: user.organizationId,
        },
      },
    });

    const recentExecutions = await this.prisma.agentExecution.count({
      where: {
        agent: {
          organizationId: user.organizationId,
        },
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
    });

    return {
      byStatus: stats.reduce(
        (acc, stat) => {
          acc[stat.status] = stat._count.id;
          return acc;
        },
        {} as Record<string, number>,
      ),
      totalExecutions,
      recentExecutions,
    };
  }
}
