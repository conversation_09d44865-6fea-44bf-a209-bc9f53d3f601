"use client";

import React, { useState, useEffect } from "react";
import { apiClient } from "@/lib/api";
import { Provider, ProviderStatus, ProviderType } from "../../../shared/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  Play,
  Trash2,
  XCircle,
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import ProviderDetails from "./ProviderDetails";
import ProviderForm from "./ProviderForm";
import ProviderTesting from "./ProviderTesting";

export default function ProviderManagement() {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(
    null,
  );
  const [showDetails, setShowDetails] = useState(false);
  const [showEdit, setShowEdit] = useState(false);
  const [showTest, setShowTest] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  const fetchProviders = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.getProviders({
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm || undefined,
        status:
          statusFilter !== "all" ? (statusFilter as ProviderStatus) : undefined,
        type: typeFilter !== "all" ? (typeFilter as ProviderType) : undefined,
        sortBy: "createdAt",
        sortOrder: "desc",
      });

      if (response.success) {
        setProviders(response.data || []);
        setPagination(response.pagination || pagination);
      } else {
        setError(response.error || "Failed to fetch providers");
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch providers",
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProviders();
  }, [pagination.page, searchTerm, statusFilter, typeFilter]);

  const handleDelete = async (provider: Provider) => {
    if (!confirm(`Are you sure you want to delete ${provider.name}?`)) return;

    try {
      await apiClient.deleteProvider(provider.id);
      fetchProviders();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to delete provider",
      );
    }
  };

  const handleTest = (provider: Provider) => {
    setSelectedProvider(provider);
    setShowTest(true);
  };

  const handleEdit = (provider: Provider) => {
    setSelectedProvider(provider);
    setShowEdit(true);
  };

  const handleView = (provider: Provider) => {
    setSelectedProvider(provider);
    setShowDetails(true);
  };

  const getStatusBadge = (status: ProviderStatus) => {
    const variants = {
      [ProviderStatus.ACTIVE]: {
        variant: "default" as const,
        icon: CheckCircle,
        color: "text-green-500",
      },
      [ProviderStatus.INACTIVE]: {
        variant: "secondary" as const,
        icon: Clock,
        color: "text-gray-500",
      },
      [ProviderStatus.ERROR]: {
        variant: "destructive" as const,
        icon: XCircle,
        color: "text-red-500",
      },
      [ProviderStatus.MAINTENANCE]: {
        variant: "outline" as const,
        icon: AlertCircle,
        color: "text-yellow-500",
      },
    };

    const config = variants[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {status}
      </Badge>
    );
  };

  const getTypeBadge = (type: ProviderType) => {
    const colors = {
      [ProviderType.OPENAI]: "bg-green-100 text-green-800",
      [ProviderType.ANTHROPIC]: "bg-orange-100 text-orange-800",
      [ProviderType.GOOGLE]: "bg-blue-100 text-blue-800",
      [ProviderType.MISTRAL]: "bg-purple-100 text-purple-800",
      [ProviderType.GROQ]: "bg-red-100 text-red-800",
      [ProviderType.CUSTOM]: "bg-gray-100 text-gray-800",
    };

    return (
      <Badge variant="outline" className={colors[type]}>
        {type.toUpperCase()}
      </Badge>
    );
  };

  if (loading && providers.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex gap-4 mb-6">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-48" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col sm:flex-row gap-4">
        <Input
          placeholder="Search providers..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value={ProviderStatus.ACTIVE}>Active</SelectItem>
            <SelectItem value={ProviderStatus.INACTIVE}>Inactive</SelectItem>
            <SelectItem value={ProviderStatus.ERROR}>Error</SelectItem>
            <SelectItem value={ProviderStatus.MAINTENANCE}>
              Maintenance
            </SelectItem>
          </SelectContent>
        </Select>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value={ProviderType.OPENAI}>OpenAI</SelectItem>
            <SelectItem value={ProviderType.ANTHROPIC}>Anthropic</SelectItem>
            <SelectItem value={ProviderType.GOOGLE}>Google</SelectItem>
            <SelectItem value={ProviderType.MISTRAL}>Mistral</SelectItem>
            <SelectItem value={ProviderType.GROQ}>Groq</SelectItem>
            <SelectItem value={ProviderType.CUSTOM}>Custom</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Providers ({pagination.total})</CardTitle>
        </CardHeader>
        <CardContent>
          {providers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No providers found. Add your first AI provider to get started.
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Models</TableHead>
                  <TableHead>Reliability</TableHead>
                  <TableHead>Executions</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {providers.map((provider) => (
                  <TableRow key={provider.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{provider.name}</div>
                        {provider.description && (
                          <div className="text-sm text-muted-foreground truncate max-w-48">
                            {provider.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getTypeBadge(provider.type)}</TableCell>
                    <TableCell>{getStatusBadge(provider.status)}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {provider.models.length} model
                        {provider.models.length !== 1 ? "s" : ""}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {Math.round(provider.reliability * 100)}%
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {provider._count?.agentExecutions ||
                          0 + provider._count?.toolExecutions ||
                          0}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm text-muted-foreground">
                        {provider.lastUsedAt
                          ? new Date(provider.lastUsedAt).toLocaleDateString()
                          : "Never"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleView(provider)}
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleTest(provider)}
                          title="Test Provider"
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEdit(provider)}
                          title="Edit Provider"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(provider)}
                          title="Delete Provider"
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
                of {pagination.total} providers
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
                  }
                  disabled={pagination.page <= 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
                  }
                  disabled={pagination.page >= pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Provider Details</DialogTitle>
          </DialogHeader>
          {selectedProvider && <ProviderDetails provider={selectedProvider} />}
        </DialogContent>
      </Dialog>

      <Dialog open={showEdit} onOpenChange={setShowEdit}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Provider</DialogTitle>
          </DialogHeader>
          {selectedProvider && (
            <ProviderForm
              provider={selectedProvider}
              onSuccess={() => {
                setShowEdit(false);
                fetchProviders();
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={showTest} onOpenChange={setShowTest}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Test Provider</DialogTitle>
          </DialogHeader>
          {selectedProvider && <ProviderTesting provider={selectedProvider} />}
        </DialogContent>
      </Dialog>
    </div>
  );
}
