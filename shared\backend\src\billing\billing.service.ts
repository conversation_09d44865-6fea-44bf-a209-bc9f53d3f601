import { Injectable, BadRequestException } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";

@Injectable()
export class BillingService {
  constructor(private prisma: PrismaService) {}

  async checkQuota(
    organizationId: string,
    resource: string,
    quantity: number,
  ): Promise<void> {
    const quota = await this.prisma.quota.findFirst({
      where: {
        organizationId,
        resource,
      },
    });

    if (quota && quota.used + quantity > quota.limit) {
      throw new BadRequestException(
        `Quota exceeded for ${resource}. Used: ${quota.used}, Limit: ${quota.limit}`,
      );
    }
  }

  async trackUsage(
    organizationId: string,
    resource: string,
    quantity: number,
    cost: number,
    metadata: Record<string, any> = {},
  ): Promise<void> {
    // Create billing usage record
    await this.prisma.billingUsage.create({
      data: {
        organizationId,
        resource,
        quantity,
        unit: "count",
        cost,
        metadata,
        timestamp: new Date(),
      },
    });

    // Update quota usage
    await this.prisma.quota.upsert({
      where: {
        organizationId_resource: {
          organizationId,
          resource,
        },
      },
      update: {
        used: {
          increment: quantity,
        },
      },
      create: {
        organizationId,
        resource,
        limit: 10000, // Default limit
        used: quantity,
        period: "monthly",
        resetAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      },
    });
  }

  async getUsage(
    organizationId: string,
    resource?: string,
    startDate?: Date,
    endDate?: Date,
  ) {
    const where: any = {
      organizationId,
    };

    if (resource) {
      where.resource = resource;
    }

    if (startDate || endDate) {
      where.timestamp = {};
      if (startDate) where.timestamp.gte = startDate;
      if (endDate) where.timestamp.lte = endDate;
    }

    return this.prisma.billingUsage.findMany({
      where,
      orderBy: {
        timestamp: "desc",
      },
    });
  }

  async getQuotas(organizationId: string) {
    return this.prisma.quota.findMany({
      where: {
        organizationId,
      },
    });
  }
}
