import { AIChat } from "@/components/ai/AIChat";

export default function Page() {
  return (
    <div className="container mx-auto p-4 h-screen flex flex-col">
      <header className="py-6">
        <h1 className="text-3xl font-bold text-center">SynapseAI</h1>
        <p className="text-center text-gray-500">
          Universal AI Orchestration System
        </p>
      </header>
      
      <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="h-full">
          <AIChat title="SynapseAI Assistant" />
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 flex flex-col">
          <h2 className="text-xl font-semibold mb-4">System Overview</h2>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-blue-500 font-medium">Agents</div>
              <div className="text-2xl font-bold">0</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm text-green-500 font-medium">Tools</div>
              <div className="text-2xl font-bold">0</div>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-sm text-purple-500 font-medium">Providers</div>
              <div className="text-2xl font-bold">0</div>
            </div>
            
            <div className="bg-amber-50 p-4 rounded-lg">
              <div className="text-sm text-amber-500 font-medium">Sessions</div>
              <div className="text-2xl font-bold">0</div>
            </div>
          </div>
          
          <div className="flex-grow flex flex-col">
            <h3 className="text-lg font-medium mb-2">Getting Started</h3>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start gap-2">
                <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">1</span>
                <span>Create an Agent to handle user requests</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">2</span>
                <span>Configure AI Providers for your organization</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">3</span>
                <span>Create Tools to extend your agent's capabilities</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">4</span>
                <span>Build Tool-Agent Hybrids for complex workflows</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}