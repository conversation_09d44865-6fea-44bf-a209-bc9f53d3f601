import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { CreateProviderDto } from "./dto/create-provider.dto";
import { UpdateProviderDto } from "./dto/update-provider.dto";
import { QueryProvidersDto } from "./dto/query-providers.dto";
import { ProviderStatus, ProviderType } from "@prisma/client";
import { BillingService } from "../billing/billing.service";
import { AnalyticsService } from "../analytics/analytics.service";

@Injectable()
export class ProvidersService {
  constructor(
    private prisma: PrismaService,
    private billingService: BillingService,
    private analyticsService: AnalyticsService,
  ) {}

  async create(
    createProviderDto: CreateProviderDto,
    userId: string,
    organizationId: string,
  ): Promise<Provider> {
    // Check quota
    await this.billingService.checkQuota(organizationId, "providers", 1);

    const provider = await this.prisma.provider.create({
      data: {
        ...createProviderDto,
        userId,
        organizationId,
      },
    });

    // Track usage
    await this.billingService.trackUsage(organizationId, "providers", 1, 0, {
      providerId: provider.id,
      action: "create",
    });

    // Track analytics
    await this.analyticsService.track("provider_created", {
      providerId: provider.id,
      organizationId,
      userId,
      type: provider.type,
      status: provider.status,
    });

    return provider as Provider;
  }

  async findAll(query: QueryProvidersDto, organizationId: string) {
    const { page, limit, search, type, status, isActive, sortBy, sortOrder } =
      query;
    const skip = (page - 1) * limit;

    const where: any = {
      organizationId,
    };

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (type) {
      where.type = type;
    }

    if (status) {
      where.status = status;
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    const [providers, total] = await Promise.all([
      this.prisma.provider.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
        include: {
          _count: {
            select: {
              agentExecutions: true,
              toolExecutions: true,
            },
          },
        },
      }),
      this.prisma.provider.count({ where }),
    ]);

    return {
      data: providers,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, organizationId: string): Promise<Provider> {
    const provider = await this.prisma.provider.findFirst({
      where: {
        id,
        organizationId,
      },
      include: {
        _count: {
          select: {
            agentExecutions: true,
            toolExecutions: true,
          },
        },
      },
    });

    if (!provider) {
      throw new NotFoundException("Provider not found");
    }

    return provider as Provider;
  }

  async update(
    id: string,
    updateProviderDto: UpdateProviderDto,
    organizationId: string,
  ): Promise<Provider> {
    const existingProvider = await this.prisma.provider.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!existingProvider) {
      throw new NotFoundException("Provider not found");
    }

    const provider = await this.prisma.provider.update({
      where: { id },
      data: updateProviderDto,
    });

    // Track analytics
    await this.analyticsService.track("provider_updated", {
      providerId: provider.id,
      organizationId,
      changes: Object.keys(updateProviderDto),
    });

    return provider as Provider;
  }

  async remove(id: string, organizationId: string): Promise<void> {
    const provider = await this.prisma.provider.findFirst({
      where: {
        id,
        organizationId,
      },
    });

    if (!provider) {
      throw new NotFoundException("Provider not found");
    }

    await this.prisma.provider.delete({
      where: { id },
    });

    // Track analytics
    await this.analyticsService.track("provider_deleted", {
      providerId: id,
      organizationId,
    });
  }

  async testProvider(id: string, organizationId: string): Promise<any> {
    const provider = await this.findOne(id, organizationId);

    if (!provider.isActive) {
      throw new BadRequestException("Provider is not active");
    }

    const startTime = Date.now();
    let testResult;
    let error: string | null = null;
    let status = "success";

    try {
      // Test the provider based on its type
      testResult = await this.executeProviderTest(provider);
    } catch (err) {
      error = err.message;
      status = "failed";
      testResult = null;
    }

    const duration = Date.now() - startTime;

    // Update provider health metrics
    await this.updateProviderHealth(
      provider.id,
      status === "success",
      duration,
    );

    // Track analytics
    await this.analyticsService.track("provider_tested", {
      providerId: provider.id,
      organizationId,
      status,
      duration,
    });

    return {
      status,
      result: testResult,
      error,
      duration,
      timestamp: new Date().toISOString(),
    };
  }

  async selectOptimalProvider(
    model: string,
    task: string,
    organizationId: string,
    context?: any,
  ): Promise<Provider> {
    const providers = await this.prisma.provider.findMany({
      where: {
        organizationId,
        isActive: true,
        status: ProviderStatus.ACTIVE,
        models: {
          has: model,
        },
      },
      include: {
        _count: {
          select: {
            agentExecutions: true,
            toolExecutions: true,
          },
        },
      },
    });

    if (providers.length === 0) {
      throw new NotFoundException(
        `No active providers found for model: ${model}`,
      );
    }

    // Score providers based on multiple factors
    const scoredProviders = providers.map((provider) => {
      const score = this.calculateProviderScore(provider, task, context);
      return { provider, score };
    });

    // Sort by score (highest first)
    scoredProviders.sort((a, b) => b.score - a.score);

    const selectedProvider = scoredProviders[0].provider;

    // Track analytics
    await this.analyticsService.track("provider_selected", {
      providerId: selectedProvider.id,
      organizationId,
      model,
      task,
      score: scoredProviders[0].score,
      alternatives: scoredProviders.length - 1,
    });

    return selectedProvider as Provider;
  }

  async getAvailableProviders() {
    return [
      {
        type: "openai",
        name: "OpenAI",
        models: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
        capabilities: ["text", "chat", "embeddings"],
      },
      {
        type: "anthropic",
        name: "Anthropic Claude",
        models: ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
        capabilities: ["text", "chat"],
      },
      {
        type: "google",
        name: "Google Gemini",
        models: ["gemini-pro", "gemini-pro-vision"],
        capabilities: ["text", "chat", "vision"],
      },
      {
        type: "mistral",
        name: "Mistral AI",
        models: ["mistral-large", "mistral-medium", "mistral-small"],
        capabilities: ["text", "chat"],
      },
      {
        type: "groq",
        name: "Groq",
        models: ["llama2-70b", "mixtral-8x7b"],
        capabilities: ["text", "chat"],
      },
    ];
  }

  async getStats(organizationId: string) {
    const [byType, byStatus, totalExecutions, recentExecutions] =
      await Promise.all([
        this.prisma.provider.groupBy({
          by: ["type"],
          where: { organizationId },
          _count: {
            type: true,
          },
        }),
        this.prisma.provider.groupBy({
          by: ["status"],
          where: { organizationId },
          _count: {
            status: true,
          },
        }),
        this.prisma.provider.aggregate({
          where: { organizationId },
          _sum: {
            totalExecutions: true,
          },
        }),
        this.prisma.provider.aggregate({
          where: {
            organizationId,
            lastUsedAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
          _count: {
            id: true,
          },
        }),
      ]);

    const typeCounts = byType.reduce((acc, item) => {
      acc[item.type] = item._count.type;
      return acc;
    }, {});

    const statusCounts = byStatus.reduce((acc, item) => {
      acc[item.status] = item._count.status;
      return acc;
    }, {});

    return {
      byType: typeCounts,
      byStatus: statusCounts,
      totalExecutions: totalExecutions._sum.totalExecutions || 0,
      recentlyUsed: recentExecutions._count.id,
    };
  }

  private async executeProviderTest(provider: any): Promise<any> {
    const startTime = Date.now();

    try {
      switch (provider.type) {
        case "OPENAI":
          return await this.testOpenAIProvider(provider, startTime);
        case "CLAUDE":
          return await this.testAnthropicProvider(provider, startTime);
        case "GEMINI":
          return await this.testGoogleProvider(provider, startTime);
        case "MISTRAL":
          return await this.testMistralProvider(provider, startTime);
        case "GROQ":
          return await this.testGroqProvider(provider, startTime);
        default:
          return {
            message: "Provider type not supported for testing",
            provider: provider.name,
            latency: Date.now() - startTime,
          };
      }
    } catch (error) {
      throw new Error(`Provider test failed: ${error.message}`);
    }
  }

  private async testOpenAIProvider(
    provider: any,
    startTime: number,
  ): Promise<any> {
    const { config } = provider;

    if (!config.apiKey) {
      throw new Error("OpenAI API key not configured");
    }

    const response = await fetch("https://api.openai.com/v1/models", {
      headers: {
        Authorization: `Bearer ${config.apiKey}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `OpenAI API error: ${error.error?.message || response.statusText}`,
      );
    }

    const data = await response.json();
    const latency = Date.now() - startTime;

    return {
      message: "OpenAI provider test successful",
      models: data.data?.map((model: any) => model.id) || [],
      latency,
      status: "healthy",
    };
  }

  private async testAnthropicProvider(
    provider: any,
    startTime: number,
  ): Promise<any> {
    const { config } = provider;

    if (!config.apiKey) {
      throw new Error("Anthropic API key not configured");
    }

    // Test with a simple message
    const response = await fetch("https://api.anthropic.com/v1/messages", {
      method: "POST",
      headers: {
        "x-api-key": config.apiKey,
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01",
      },
      body: JSON.stringify({
        model: "claude-3-haiku-20240307",
        max_tokens: 10,
        messages: [{ role: "user", content: "Hello" }],
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `Anthropic API error: ${error.error?.message || response.statusText}`,
      );
    }

    const latency = Date.now() - startTime;

    return {
      message: "Anthropic provider test successful",
      models: [
        "claude-3-opus-20240229",
        "claude-3-sonnet-20240229",
        "claude-3-haiku-20240307",
      ],
      latency,
      status: "healthy",
    };
  }

  private async testGoogleProvider(
    provider: any,
    startTime: number,
  ): Promise<any> {
    const { config } = provider;

    if (!config.apiKey) {
      throw new Error("Google API key not configured");
    }

    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models?key=${config.apiKey}`,
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `Google API error: ${error.error?.message || response.statusText}`,
      );
    }

    const data = await response.json();
    const latency = Date.now() - startTime;

    return {
      message: "Google provider test successful",
      models:
        data.models?.map((model: any) => model.name.split("/").pop()) || [],
      latency,
      status: "healthy",
    };
  }

  private async testMistralProvider(
    provider: any,
    startTime: number,
  ): Promise<any> {
    const { config } = provider;

    if (!config.apiKey) {
      throw new Error("Mistral API key not configured");
    }

    const response = await fetch("https://api.mistral.ai/v1/models", {
      headers: {
        Authorization: `Bearer ${config.apiKey}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `Mistral API error: ${error.message || response.statusText}`,
      );
    }

    const data = await response.json();
    const latency = Date.now() - startTime;

    return {
      message: "Mistral provider test successful",
      models: data.data?.map((model: any) => model.id) || [],
      latency,
      status: "healthy",
    };
  }

  private async testGroqProvider(
    provider: any,
    startTime: number,
  ): Promise<any> {
    const { config } = provider;

    if (!config.apiKey) {
      throw new Error("Groq API key not configured");
    }

    const response = await fetch("https://api.groq.com/openai/v1/models", {
      headers: {
        Authorization: `Bearer ${config.apiKey}`,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(
        `Groq API error: ${error.error?.message || response.statusText}`,
      );
    }

    const data = await response.json();
    const latency = Date.now() - startTime;

    return {
      message: "Groq provider test successful",
      models: data.data?.map((model: any) => model.id) || [],
      latency,
      status: "healthy",
    };
  }

  private calculateProviderScore(
    provider: any,
    task: string,
    context?: any,
  ): number {
    let score = 0;

    // Base score from reliability
    score += provider.reliability * 40;

    // Cost efficiency (lower cost = higher score)
    score += (1 - provider.costPerToken) * 30;

    // Performance (lower latency = higher score)
    score += (1 - provider.averageLatency / 5000) * 20;

    // Usage count (more usage = higher reliability)
    score += Math.min(provider.totalExecutions / 1000, 1) * 10;

    return Math.max(0, Math.min(100, score));
  }

  private async updateProviderHealth(
    providerId: string,
    success: boolean,
    latency: number,
  ): Promise<void> {
    const provider = await this.prisma.provider.findUnique({
      where: { id: providerId },
    });

    if (!provider) return;

    const newReliability = success
      ? Math.min(1, provider.reliability + 0.01)
      : Math.max(0, provider.reliability - 0.05);

    const newAverageLatency = provider.averageLatency * 0.9 + latency * 0.1;

    await this.prisma.provider.update({
      where: { id: providerId },
      data: {
        reliability: newReliability,
        averageLatency: newAverageLatency,
        lastUsedAt: new Date(),
        totalExecutions: {
          increment: 1,
        },
      },
    });
  }
}