import { <PERSON>du<PERSON> } from '@nestjs/common';
import { HITLService } from './hitl.service';
import { HITLController } from './hitl.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { ApixModule } from '../apix/apix.module';
import { AnalyticsModule } from '../analytics/analytics.module';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [PrismaModule, ApixModule, AnalyticsModule, NotificationsModule],
  controllers: [HITLController],
  providers: [HITLService],
  exports: [HITLService],
})
export class HITLModule {}