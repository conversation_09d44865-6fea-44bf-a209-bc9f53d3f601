"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Tool, ToolStatus } from "../../../shared/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { X, Plus } from "lucide-react";

interface ToolFormProps {
  tool?: Tool;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

interface FormData {
  name: string;
  description: string;
  schema: string;
  endpoint: string;
  method: string;
  headers: string;
  authentication: string;
  status: ToolStatus;
  isPublic: boolean;
  tags: string[];
  category: string;
  metadata: string;
}

const HTTP_METHODS = ["GET", "POST", "PUT", "PATCH", "DELETE"];
const TOOL_CATEGORIES = [
  "API Integration",
  "Data Processing",
  "Communication",
  "File Management",
  "Database",
  "Analytics",
  "Automation",
  "Other",
];

export function ToolForm({ tool, onSubmit, onCancel }: ToolFormProps) {
  const [tags, setTags] = useState<string[]>(tool?.tags || []);
  const [newTag, setNewTag] = useState("");
  const [schemaError, setSchemaError] = useState("");
  const [headersError, setHeadersError] = useState("");
  const [authError, setAuthError] = useState("");
  const [metadataError, setMetadataError] = useState("");

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    defaultValues: {
      name: tool?.name || "",
      description: tool?.description || "",
      schema: tool?.schema
        ? JSON.stringify(tool.schema, null, 2)
        : JSON.stringify(
            {
              type: "object",
              properties: {
                input: {
                  type: "string",
                  description: "Input parameter",
                },
              },
              required: ["input"],
            },
            null,
            2,
          ),
      endpoint: tool?.endpoint || "",
      method: tool?.method || "POST",
      headers: tool?.headers
        ? JSON.stringify(tool.headers, null, 2)
        : JSON.stringify(
            {
              "Content-Type": "application/json",
            },
            null,
            2,
          ),
      authentication: tool?.authentication
        ? JSON.stringify(tool.authentication, null, 2)
        : JSON.stringify(
            {
              type: "none",
            },
            null,
            2,
          ),
      status: tool?.status || ToolStatus.DRAFT,
      isPublic: tool?.isPublic || false,
      category: tool?.category || "",
      metadata: tool?.metadata
        ? JSON.stringify(tool.metadata, null, 2)
        : JSON.stringify({}, null, 2),
    },
  });

  const validateJSON = (value: string, setError: (error: string) => void) => {
    try {
      JSON.parse(value);
      setError("");
      return true;
    } catch (error) {
      setError("Invalid JSON format");
      return false;
    }
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const onFormSubmit = (data: FormData) => {
    // Validate JSON fields
    const schemaValid = validateJSON(data.schema, setSchemaError);
    const headersValid = validateJSON(data.headers, setHeadersError);
    const authValid = validateJSON(data.authentication, setAuthError);
    const metadataValid = validateJSON(data.metadata, setMetadataError);

    if (!schemaValid || !headersValid || !authValid || !metadataValid) {
      return;
    }

    const formattedData = {
      ...data,
      schema: JSON.parse(data.schema),
      headers: JSON.parse(data.headers),
      authentication: JSON.parse(data.authentication),
      metadata: JSON.parse(data.metadata),
      tags,
    };

    onSubmit(formattedData);
  };

  return (
    <div className="bg-white">
      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="config">Configuration</TabsTrigger>
            <TabsTrigger value="auth">Authentication</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name *</Label>
                    <Input
                      id="name"
                      {...register("name", { required: "Name is required" })}
                      placeholder="Enter tool name"
                    />
                    {errors.name && (
                      <p className="text-sm text-red-600">
                        {errors.name.message}
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={watch("category")}
                      onValueChange={(value) => setValue("category", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {TOOL_CATEGORIES.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    {...register("description")}
                    placeholder="Describe what this tool does"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {tags.map((tag) => (
                      <Badge
                        key={tag}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag"
                      onKeyPress={(e) =>
                        e.key === "Enter" && (e.preventDefault(), addTag())
                      }
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={watch("status")}
                      onValueChange={(value) =>
                        setValue("status", value as ToolStatus)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={ToolStatus.DRAFT}>Draft</SelectItem>
                        <SelectItem value={ToolStatus.ACTIVE}>
                          Active
                        </SelectItem>
                        <SelectItem value={ToolStatus.ARCHIVED}>
                          Archived
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="isPublic">Public Tool</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isPublic"
                        checked={watch("isPublic")}
                        onCheckedChange={(checked) =>
                          setValue("isPublic", checked)
                        }
                      />
                      <Label
                        htmlFor="isPublic"
                        className="text-sm text-gray-600"
                      >
                        Make this tool available to other organizations
                      </Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="config" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>API Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="col-span-2 space-y-2">
                    <Label htmlFor="endpoint">Endpoint URL</Label>
                    <Input
                      id="endpoint"
                      {...register("endpoint")}
                      placeholder="https://api.example.com/endpoint"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="method">HTTP Method</Label>
                    <Select
                      value={watch("method")}
                      onValueChange={(value) => setValue("method", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {HTTP_METHODS.map((method) => (
                          <SelectItem key={method} value={method}>
                            {method}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="schema">JSON Schema *</Label>
                  <Textarea
                    id="schema"
                    {...register("schema", { required: "Schema is required" })}
                    placeholder="Define the input schema for this tool"
                    rows={8}
                    className="font-mono text-sm"
                    onChange={(e) =>
                      validateJSON(e.target.value, setSchemaError)
                    }
                  />
                  {schemaError && (
                    <p className="text-sm text-red-600">{schemaError}</p>
                  )}
                  {errors.schema && (
                    <p className="text-sm text-red-600">
                      {errors.schema.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="headers">Headers</Label>
                  <Textarea
                    id="headers"
                    {...register("headers")}
                    placeholder="HTTP headers as JSON"
                    rows={4}
                    className="font-mono text-sm"
                    onChange={(e) =>
                      validateJSON(e.target.value, setHeadersError)
                    }
                  />
                  {headersError && (
                    <p className="text-sm text-red-600">{headersError}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="auth" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Authentication</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="authentication">
                    Authentication Configuration
                  </Label>
                  <Textarea
                    id="authentication"
                    {...register("authentication")}
                    placeholder="Authentication configuration as JSON"
                    rows={6}
                    className="font-mono text-sm"
                    onChange={(e) => validateJSON(e.target.value, setAuthError)}
                  />
                  {authError && (
                    <p className="text-sm text-red-600">{authError}</p>
                  )}
                  <p className="text-sm text-gray-600">
                    Configure authentication method (API key, OAuth, etc.)
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="metadata">Metadata</Label>
                  <Textarea
                    id="metadata"
                    {...register("metadata")}
                    placeholder="Additional metadata as JSON"
                    rows={6}
                    className="font-mono text-sm"
                    onChange={(e) =>
                      validateJSON(e.target.value, setMetadataError)
                    }
                  />
                  {metadataError && (
                    <p className="text-sm text-red-600">{metadataError}</p>
                  )}
                  <p className="text-sm text-gray-600">
                    Store additional configuration or documentation
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : tool ? "Update Tool" : "Create Tool"}
          </Button>
        </div>
      </form>
    </div>
  );
}
