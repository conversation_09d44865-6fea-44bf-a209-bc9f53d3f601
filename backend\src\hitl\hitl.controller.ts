import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  Req,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { HITLService } from './hitl.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthenticatedRequest } from '../common/middleware/tenant.middleware';
import { HITLDecisionType, HITLPriority, HITLRequestType, HITLStatus } from '../../../shared/types';

@Controller('api/v1/hitl')
@UseGuards(JwtAuthGuard)
export class HITLController {
  constructor(private readonly hitlService: HITLService) {}

  @Post('requests')
  async createRequest(
    @Body()
    createRequestDto: {
      type: HITLRequestType;
      title: string;
      description?: string;
      data?: Record<string, any>;
      priority?: HITLPriority;
      sessionId?: string;
      assigneeId?: string;
    },
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      const request = await this.hitlService.createRequest({
        ...createRequestDto,
        userId: req.user?.id || "",
        organizationId: req.user?.organizationId || "",
      });

      return {
        success: true,
        data: request,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to create HITL request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('requests')
  async findAll(
    @Query('status') status: HITLStatus,
    @Query('type') type: HITLRequestType,
    @Query('priority') priority: HITLPriority,
    @Query('userId') userId: string,
    @Query('assigneeId') assigneeId: string,
    @Query('sessionId') sessionId: string,
    @Query('limit') limit: number,
    @Query('offset') offset: number,
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      const result = await this.hitlService.findAll(
        req.user?.organizationId || "",
        {
          status,
          type,
          priority,
          userId,
          assigneeId,
          sessionId,
          limit: limit ? Number(limit) : undefined,
          offset: offset ? Number(offset) : undefined,
        },
      );

      return {
        success: true,
        data: result.data,
        meta: result.meta,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to fetch HITL requests',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('requests/:id')
  async findOne(@Param('id') id: string, @Req() req: AuthenticatedRequest) {
    try {
      const request = await this.hitlService.findOne(id);

      // Check if user has access to this request
      if (request.organizationId !== req.user?.organizationId) {
        throw new HttpException(
          'You do not have permission to access this HITL request',
          HttpStatus.FORBIDDEN,
        );
      }

      return {
        success: true,
        data: request,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to fetch HITL request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch('requests/:id')
  async update(
    @Param('id') id: string,
    @Body()
    updateRequestDto: {
      title?: string;
      description?: string;
      data?: Record<string, any>;
      priority?: HITLPriority;
      status?: HITLStatus;
      assigneeId?: string;
    },
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      // Check if user has access to this request
      const request = await this.hitlService.findOne(id);
      if (request.organizationId !== req.user?.organizationId) {
        throw new HttpException(
          'You do not have permission to update this HITL request',
          HttpStatus.FORBIDDEN,
        );
      }

      const updatedRequest = await this.hitlService.update(
        id,
        updateRequestDto,
      );

      return {
        success: true,
        data: updatedRequest,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to update HITL request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('requests/:id/decision')
  async makeDecision(
    @Param('id') id: string,
    @Body()
    decisionDto: {
      decision: HITLDecisionType;
      reasoning?: string;
      data?: Record<string, any>;
    },
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      // Check if user has access to this request
      const request = await this.hitlService.findOne(id);
      if (request.organizationId !== req.user?.organizationId) {
        throw new HttpException(
          'You do not have permission to make decisions on this HITL request',
          HttpStatus.FORBIDDEN,
        );
      }

      // Check if user has appropriate role for making decisions
      if (!['SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER'].includes(req.user?.role || '')) {
        throw new HttpException(
          'You do not have permission to make decisions on HITL requests',
          HttpStatus.FORBIDDEN,
        );
      }

      const result = await this.hitlService.makeDecision(id, {
        ...decisionDto,
        userId: req.user?.id || "",
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to make decision on HITL request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('requests/:id/assign')
  async assignRequest(
    @Param('id') id: string,
    @Body() assignDto: { assigneeId: string },
    @Req() req: AuthenticatedRequest,
  ) {
    try {
      // Check if user has access to this request
      const request = await this.hitlService.findOne(id);
      if (request.organizationId !== req.user?.organizationId) {
        throw new HttpException(
          'You do not have permission to assign this HITL request',
          HttpStatus.FORBIDDEN,
        );
      }

      // Check if user has appropriate role for assigning requests
      if (!['SUPER_ADMIN', 'ORG_ADMIN'].includes(req.user?.role || '')) {
        throw new HttpException(
          'You do not have permission to assign HITL requests',
          HttpStatus.FORBIDDEN,
        );
      }

      const updatedRequest = await this.hitlService.assignRequest(
        id,
        assignDto.assigneeId,
      );

      return {
        success: true,
        data: updatedRequest,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to assign HITL request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('stats')
  async getStats(@Req() req: AuthenticatedRequest) {
    try {
      const stats = await this.hitlService.getStats(req.user?.organizationId || "");

      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to fetch HITL stats',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}