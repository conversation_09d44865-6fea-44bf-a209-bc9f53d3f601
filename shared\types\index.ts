// Shared TypeScript types across frontend and backend

// User types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  organizationId: string;
}

export enum UserRole {
  SUPER_ADMIN = "SUPER_ADMIN",
  ORG_ADMIN = "ORG_ADMIN",
  DEVELOPER = "DEVELOPER",
  VIEWER = "VIEWER",
}

// Organization types
export interface Organization {
  id: string;
  name: string;
  slug: string;
  description?: string;
  settings: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Agent types
export interface Agent {
  id: string;
  name: string;
  description?: string;
  prompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
  status: AgentStatus;
  version: number;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  organizationId: string;
  metadata: Record<string, any>;
  tags: string[];
  templateId?: string;
  templateVersion?: number;
  templateName?: string;
  templateType?: string;
  templateDescription?: string;
  templateCreatedAt?: Date;
  templateUpdatedAt?: Date;
}

export enum AgentStatus {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  ARCHIVED = "ARCHIVED",
}

// Tool types
export interface Tool {
  id: string;
  name: string;
  description?: string;
  schema: Record<string, any>;
  endpoint?: string;
  method: string;
  headers: Record<string, any>;
  authentication: Record<string, any>;
  status: ToolStatus;
  version: number;
  isPublic: boolean;
  tags: string[];
  category?: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  organizationId: string;
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  executions?: ToolExecution[];
  _count?: {
    executions: number;
    hybrids: number;
    workflows: number;
  };
}

export interface ToolExecution {
  id: string;
  status: string;
  input: Record<string, any>;
  output?: Record<string, any>;
  error?: string;
  duration?: number;
  cost?: number;
  createdAt: Date;
  completedAt?: Date;
  toolId: string;
}

export enum ToolStatus {
  DRAFT = "DRAFT",
  ACTIVE = "ACTIVE",
  ARCHIVED = "ARCHIVED",
}

// Session types
export interface Session {
  id: string;
  title?: string;
  context: Record<string, any>;
  memory: Record<string, any>;
  status: SessionStatus;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  organizationId: string;
  agentId?: string;
  hybridId?: string;
}

export enum SessionStatus {
  ACTIVE = "ACTIVE",
  PAUSED = "PAUSED",
  COMPLETED = "COMPLETED",
  ARCHIVED = "ARCHIVED",
}

// Message types
export interface Message {
  id: string;
  content: string;
  role: MessageRole;
  metadata: Record<string, any>;
  createdAt: Date;
  sessionId: string;
}

export enum MessageRole {
  USER = "USER",
  ASSISTANT = "ASSISTANT",
  SYSTEM = "SYSTEM",
  TOOL = "TOOL",
}

// HITL types
export interface HITLRequest {
  id: string;
  type: HITLRequestType;
  title: string;
  description?: string;
  data: Record<string, any>;
  status: HITLStatus;
  priority: HITLPriority;
  response?: Record<string, any>;
  responseAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  assigneeId?: string;
  sessionId?: string;
  organizationId: string;
}

export enum HITLRequestType {
  APPROVAL = "APPROVAL",
  REVIEW = "REVIEW",
  CONSULTATION = "CONSULTATION",
  ESCALATION = "ESCALATION",
}

export enum HITLDecisionType {
  APPROVE = "APPROVE",
  REJECT = "REJECT",
  ESCALATE = "ESCALATE",
}

export enum HITLStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED",
  ESCALATED = "ESCALATED",
  COMPLETED = "COMPLETED",
}

export enum HITLPriority {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  URGENT = "URGENT",
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// APIX Event types
export interface ApixEvent {
  type: string;
  payload: Record<string, any>;
  timestamp: Date;
  organizationId: string;
  userId?: string;
}


// Provider types
export interface Provider {
  id: string;
  name: string;
  description?: string;
  type: ProviderType;
  configuration: Record<string, any>;
  models: string[];
  capabilities: string[];
  status: ProviderStatus;
  isActive: boolean;
  reliability: number;
  costPerToken: number;
  averageLatency: number;
  priority: number;
  totalExecutions: number;
  lastUsedAt?: Date;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  organizationId: string;
  _count?: {
    agentExecutions: number;
    toolExecutions: number;
  };
}

export enum ProviderType {
  OPENAI = "openai",
  ANTHROPIC = "anthropic",
  GOOGLE = "google",
  MISTRAL = "mistral",
  GROQ = "groq",
  CUSTOM = "custom",
}

export enum ProviderStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  ERROR = "ERROR",
  MAINTENANCE = "MAINTENANCE",
}


// Billing types
export interface BillingUsage {
  id: string;
  resource: string;
  quantity: number;
  unit: string;
  cost: number;
  metadata: Record<string, any>;
  timestamp: Date;
  organizationId: string;
}

export interface Quota {
  id: string;
  resource: string;
  limit: number;
  used: number;
  period: string;
  resetAt: Date;
  createdAt: Date;
  updatedAt: Date;
  organizationId: string;
}
