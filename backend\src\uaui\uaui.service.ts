import { Injectable, Logger } from "@nestjs/common";
import { PrismaService } from "../prisma/prisma.service";
import { ProvidersService } from "../providers/providers.service";
import { ToolsService } from "../tools/tools.service";
import { AgentsService } from "../agents/agents.service";
import { SessionsService } from "../sessions/sessions.service";
import { ApixService } from "../apix/apix.service";
import { BillingService } from "../billing/billing.service";
import { AnalyticsService } from "../analytics/analytics.service";
import { SmartProviderSelector } from "./smart-provider-selector";
import { StateManager } from "./state-manager";
import { EventBus } from "./event-bus";
import { RouterEngine } from "./router-engine";

export interface UAUIRequest {
  userId: string;
  organizationId: string;
  sessionId?: string;
  message: string;
  agentId?: string;
  toolId?: string;
  hybridId?: string;
  appType?: string;
  metadata?: Record<string, any>;
  context?: Record<string, any>;
}

export interface UAUIResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
  hitl_request?: {
    type: string;
    data: Record<string, any>;
  };
}

@Injectable()
export class UAUIService {
  private readonly logger = new Logger(UAUIService.name);

  constructor(
    private prisma: PrismaService,
    private providersService: ProvidersService,
    private toolsService: ToolsService,
    private agentsService: AgentsService,
    private sessionsService: SessionsService,
    private apixService: ApixService,
    private billingService: BillingService,
    private analyticsService: AnalyticsService,
    private smartProviderSelector: SmartProviderSelector,
    private stateManager: StateManager,
    private eventBus: EventBus,
    private routerEngine: RouterEngine
  ) {}

  async processRequest(request: UAUIRequest): Promise<UAUIResponse> {
    this.logger.log(`Processing UAUI request from user ${request.userId}`);
    
    try {
      // Track request
      await this.analyticsService.track("uaui_request", {
        userId: request.userId,
        organizationId: request.organizationId,
        sessionId: request.sessionId,
        agentId: request.agentId,
        toolId: request.toolId,
        hybridId: request.hybridId,
        appType: request.appType,
      });

      // Check quota
      await this.billingService.checkQuota(
        request.organizationId,
        "uaui_requests",
        1
      );

      // Get or create session
      const session = await this.getOrCreateSession(request);

      // Emit thinking status
      await this.apixService.emit({
        type: "thinking_status",
        payload: {
          status: "thinking",
          sessionId: session.id,
        },
        organizationId: request.organizationId,
        userId: request.userId,
        sessionId: session.id,
      });

      // Process based on request type
      let response: UAUIResponse;

      if (request.agentId) {
        response = await this.processAgentRequest(request, session);
      } else if (request.toolId) {
        response = await this.processToolRequest(request, session);
      } else if (request.hybridId) {
        response = await this.processHybridRequest(request, session);
      } else {
        // Default to smart routing
        response = await this.processSmartRoutingRequest(request, session);
      }

      // Update session state
      await this.stateManager.updateSessionState(session.id, {
        lastResponse: response.final || "",
        lastInteraction: new Date().toISOString(),
      });

      // Track completion
      await this.analyticsService.track("uaui_response", {
        userId: request.userId,
        organizationId: request.organizationId,
        sessionId: session.id,
        success: !response.error,
        hasToolCall: !!response.tool_call,
        hasHitlRequest: !!response.hitl_request,
      });

      return response;
    } catch (error) {
      this.logger.error(`Error processing UAUI request: ${error.message}`, error.stack);
      
      // Emit error event
      if (request.sessionId) {
        await this.apixService.emit({
          type: "error",
          payload: {
            error: error.message,
            sessionId: request.sessionId,
          },
          organizationId: request.organizationId,
          userId: request.userId,
          sessionId: request.sessionId,
        });
      }

      return {
        error: error.message,
      };
    }
  }

  private async getOrCreateSession(request: UAUIRequest) {
    if (request.sessionId) {
      const existingSession = await this.sessionsService.findOne(request.sessionId);
      if (existingSession) {
        return existingSession;
      }
    }

    // Create new session
    return await this.sessionsService.create({
      title: `Session ${new Date().toISOString()}`,
      context: request.context || {},
      agentId: request.agentId,
      hybridId: request.hybridId,
      userId: request.userId,
      organizationId: request.organizationId,
    });
  }

  private async processAgentRequest(request: UAUIRequest, session: any): Promise<UAUIResponse> {
    // Execute agent with the message
    const result = await this.agentsService.execute(
      request.agentId,
      {
        input: request.message,
        sessionId: session.id,
        context: request.context || {},
      },
      { id: request.userId, organizationId: request.organizationId }
    );

    // Store message in session
    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: request.message,
      role: "USER",
      metadata: request.metadata || {},
    });

    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: result.response.content,
      role: "ASSISTANT",
      metadata: {
        executionId: result.executionId,
        tokens: result.tokens,
        cost: result.cost,
        duration: result.duration,
      },
    });

    return {
      final: result.response.content,
      state_update: {
        executionId: result.executionId,
        tokens: result.tokens,
        cost: result.cost,
        duration: result.duration,
      },
    };
  }

  private async processToolRequest(request: UAUIRequest, session: any): Promise<UAUIResponse> {
    // Parse input from message or use context
    let input: Record<string, any>;
    
    try {
      input = JSON.parse(request.message);
    } catch (e) {
      // If not valid JSON, use as a single input parameter
      input = { input: request.message };
    }

    // Execute tool
    const result = await this.toolsService.execute(
      request.toolId,
      {
        input,
        sessionId: session.id,
        context: request.context || {},
      },
      { id: request.userId, organizationId: request.organizationId }
    );

    // Store message in session
    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: JSON.stringify(input),
      role: "USER",
      metadata: request.metadata || {},
    });

    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: JSON.stringify(result.result),
      role: "TOOL",
      metadata: {
        executionId: result.executionId,
        cost: result.cost,
        duration: result.duration,
      },
    });

    return {
      final: JSON.stringify(result.result),
      state_update: {
        executionId: result.executionId,
        cost: result.cost,
        duration: result.duration,
      },
    };
  }

  private async processHybridRequest(request: UAUIRequest, session: any): Promise<UAUIResponse> {
    // Get the hybrid workflow
    const hybrid = await this.prisma.hybrid.findUnique({
      where: { id: request.hybridId },
      include: {
        agents: {
          include: {
            agent: true,
          },
          orderBy: {
            order: 'asc',
          },
        },
        tools: {
          include: {
            tool: true,
          },
          orderBy: {
            order: 'asc',
          },
        },
      },
    });

    if (!hybrid) {
      throw new NotFoundException(`Hybrid with ID ${request.hybridId} not found`);
    }

    // Store user message in session
    await this.sessionsService.addMessage({
      sessionId: session.id,
      content: request.message,
      role: "USER",
      metadata: request.metadata || {},
    });

    // Execute the hybrid workflow
    const startTime = Date.now();
    let currentContext = { ...request.context, input: request.message };
    let finalResponse = '';
    let toolCallResult = null;
    let totalTokens = 0;
    let totalCost = 0;

    try {
      // Create execution record
      const execution = await this.prisma.hybridExecution.create({
        data: {
          hybridId: request.hybridId,
          status: "RUNNING",
          input: {
            message: request.message,
            context: request.context,
          },
        },
      });

      // Execute each agent in sequence
      for (const agentLink of hybrid.agents) {
        const agent = agentLink.agent;
        
        // Execute agent with current context
        const agentResult = await this.agentsService.execute(
          agent.id,
          {
            input: typeof currentContext.input === 'string' ? currentContext.input : JSON.stringify(currentContext.input),
            sessionId: session.id,
            context: currentContext,
          },
          { id: request.userId, organizationId: request.organizationId }
        );
        
        // Update context with agent result
        currentContext = {
          ...currentContext,
          agentResult: agentResult.response.content,
          lastAgent: agent.name,
        };
        
        // Track tokens and cost
        totalTokens += agentResult.tokens || 0;
        totalCost += agentResult.cost || 0;
      }

      // Execute each tool in sequence
      for (const toolLink of hybrid.tools) {
        const tool = toolLink.tool;
        
        // Parse input from context
        let toolInput: Record<string, any>;
        try {
          toolInput = typeof currentContext.input === 'string' ? 
            JSON.parse(currentContext.input) : currentContext.input;
        } catch (e) {
          toolInput = { input: currentContext.input };
        }
        
        // Execute tool with current context
        const toolResult = await this.toolsService.execute(
          tool.id,
          {
            input: toolInput,
            sessionId: session.id,
            context: currentContext,
          },
          { id: request.userId, organizationId: request.organizationId }
        );
        
        // Update context with tool result
        currentContext = {
          ...currentContext,
          toolResult: toolResult.result,
          lastTool: tool.name,
        };
        
        // Save tool call result for potential return
        toolCallResult = {
          toolId: tool.id,
          params: toolInput,
        };
        
        // Track cost
        totalCost += toolResult.cost || 0;
      }

      // Generate final response using the last agent
      if (hybrid.agents.length > 0) {
        const finalAgent = hybrid.agents[hybrid.agents.length - 1].agent;
        const finalAgentResult = await this.agentsService.execute(
          finalAgent.id,
          {
            input: "Generate a final response based on the context",
            sessionId: session.id,
            context: currentContext,
          },
          { id: request.userId, organizationId: request.organizationId }
        );
        
        finalResponse = finalAgentResult.response.content;
        totalTokens += finalAgentResult.tokens || 0;
        totalCost += finalAgentResult.cost || 0;
      } else {
        // If no agents, use the tool result as the final response
        finalResponse = JSON.stringify(currentContext.toolResult || currentContext);
      }

      // Store assistant message in session
      await this.sessionsService.addMessage({
        sessionId: session.id,
        content: finalResponse,
        role: "ASSISTANT",
        metadata: {
          executionId: execution.id,
          tokens: totalTokens,
          cost: totalCost,
          duration: Date.now() - startTime,
        },
      });

      // Update execution record
      await this.prisma.hybridExecution.update({
        where: { id: execution.id },
        data: {
          status: "COMPLETED",
          output: { response: finalResponse },
          duration: Date.now() - startTime,
          cost: totalCost,
          completedAt: new Date(),
        },
      });

      return {
        final: finalResponse,
        tool_call: toolCallResult,
        state_update: {
          executionId: execution.id,
          tokens: totalTokens,
          cost: totalCost,
          duration: Date.now() - startTime,
        },
      };
    } catch (error) {
      this.logger.error(`Error executing hybrid workflow: ${error.message}`, error.stack);
      
      throw error;
    }
  }

  private async processSmartRoutingRequest(request: UAUIRequest, session: any): Promise<UAUIResponse> {
    // Analyze the request and determine the best way to handle it
    // For now, default to using a default agent
    
    // Get default agent for the organization
    const defaultAgent = await this.prisma.agent.findFirst({
      where: {
        organizationId: request.organizationId,
        status: "ACTIVE",
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!defaultAgent) {
      return {
        final: "No active agents found for this organization. Please create an agent first.",
        error: "No active agents found",
      };
    }

    // Process with the default agent
    return await this.processAgentRequest(
      {
        ...request,
        agentId: defaultAgent.id,
      },
      session
    );
  }
}